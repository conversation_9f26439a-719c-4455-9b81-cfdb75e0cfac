<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="ic_launcher_background">#FFFFFF</color>
    <string name="firebase_database_url" translatable="false">https://chatapp-709b9-default-rtdb.firebaseio.com</string>
    <string name="gcm_defaultSenderId" translatable="false">105728719471</string>
    <string name="google_api_key" translatable="false">AIzaSyAM9CtshK_uJW0vUbSYda2Yk6ZPoTGNYWY</string>
    <string name="google_app_id" translatable="false">1:105728719471:android:88d28ae970b043bd0feb9a</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyAM9CtshK_uJW0vUbSYda2Yk6ZPoTGNYWY</string>
    <string name="google_storage_bucket" translatable="false">chatapp-709b9.firebasestorage.app</string>
    <string name="project_id" translatable="false">chatapp-709b9</string>
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
</resources>