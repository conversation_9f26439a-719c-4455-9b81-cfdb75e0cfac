import 'package:flutter/material.dart';
import 'dart:async';
import '../services/service_provider.dart';
import '../services/call_service.dart';
import '../utils/avatar_helper.dart';
import '../providers/user_settings_provider.dart';
import 'package:provider/provider.dart';
import 'call_screen.dart';

class WaitingScreen extends StatefulWidget {
  final String queueKey;
  final Map<String, dynamic> preferences;
  final int timeoutMs;

  const WaitingScreen({
    super.key,
    required this.queueKey,
    required this.preferences,
    required this.timeoutMs,
  });

  @override
  State<WaitingScreen> createState() => _WaitingScreenState();
}

class _WaitingScreenState extends State<WaitingScreen>
    with SingleTickerProviderStateMixin {
  final _serviceProvider = ServiceProvider();
  late CallService _callService;
  late StreamSubscription _callStatusSubscription;

  // Animation controller for pulsing effect
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  // State variables
  int _elapsedTimeInSeconds = 0;
  String _status = 'Поиск собеседника...';
  bool _isRelaxedMatching = false;
  Timer? _elapsedTimeTimer;

  @override
  void initState() {
    super.initState();
    _callService = _serviceProvider.callService;

    // Set up animation
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Start pulsing animation
    _animationController.repeat(reverse: true);

    // Start timer to track elapsed time
    _startElapsedTimeTimer();

    // Listen for call status updates
    _setupCallStatusListener();
  }

  void _startElapsedTimeTimer() {
    _elapsedTimeTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _elapsedTimeInSeconds++;

          // Update status based on elapsed time
          if (_elapsedTimeInSeconds > 30 && !_isRelaxedMatching) {
            _status = 'Расширенный поиск...';
          } else if (_elapsedTimeInSeconds > 15) {
            _status = 'Продолжаем поиск...';
          }
        });
      }
    });
  }

  void _setupCallStatusListener() {
    _callStatusSubscription = _callService.callStatusStream.listen((
      statusUpdate,
    ) {
      if (!mounted) return;

      if (statusUpdate['event'] == 'relaxedMatchFound') {
        setState(() {
          _isRelaxedMatching = true;
          _status = 'Расширенный поиск активирован...';
        });
      } else if (statusUpdate['event'] == 'callConnected') {
        // A match was found, navigate to call screen
        _navigateToCallScreen(
          statusUpdate['callId'],
          statusUpdate['matchedUserId'],
          true, // Caller is the initiator
        );
      } else if (statusUpdate['event'] == 'queueTimeout') {
        // Queue timed out, go back to previous screen
        _showTimeoutMessage(
          statusUpdate['message'] ?? 'Время ожидания истекло',
        );
        Navigator.of(context).pop();
      } else if (statusUpdate['event'] == 'waitingUpdate') {
        // Just a waiting update, no UI change needed
      }
    });
  }

  void _navigateToCallScreen(
    String callId,
    String remoteUserId,
    bool isInitiator,
  ) {
    // Use a post-frame callback to avoid BuildContext issues
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // Replace the waiting screen with the call screen
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder:
              (context) => CallScreen(
                callId: callId,
                remoteUserId: remoteUserId,
                isInitiator: isInitiator,
              ),
        ),
      );
    });
  }

  void _showTimeoutMessage(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  void _cancelCall() {
    _callService.cancelCall(widget.queueKey, 'any');
    Navigator.of(context).pop();
  }

  String _formatElapsedTime() {
    final minutes = _elapsedTimeInSeconds ~/ 60;
    final seconds = _elapsedTimeInSeconds % 60;
    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  String _getPreferenceText() {
    final gender = widget.preferences['gender'] ?? 'any';
    final region = widget.preferences['region'] ?? 'Russia';

    String genderText =
        gender == 'male'
            ? 'мужчину'
            : gender == 'female'
            ? 'женщину'
            : 'собеседника';

    return 'Ищем $genderText из $region';
  }

  @override
  void dispose() {
    _animationController.dispose();
    _elapsedTimeTimer?.cancel();
    _callStatusSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final provider = Provider.of<UserSettingsProvider>(context);

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          _cancelCall();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Поиск собеседника'),
          centerTitle: true,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _cancelCall,
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Elapsed time
                Text(
                  'Время поиска: ${_formatElapsedTime()}',
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode ? Colors.grey[300] : Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 40),

                // Animated avatar
                AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _pulseAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(
                                context,
                              ).primaryColor.withAlpha(100),
                              blurRadius: 15,
                              spreadRadius: 5,
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(60),
                          child: Image.asset(
                            AvatarHelper.getLargeAvatarAsset(
                              provider.preferredGender,
                            ),
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.person,
                                size: 60,
                                color:
                                    isDarkMode
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                              );
                            },
                          ),
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(height: 40),

                // Status text
                Text(
                  _status,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDarkMode ? Colors.white : Colors.black87,
                  ),
                ),
                const SizedBox(height: 10),

                // Preference text
                Text(
                  _getPreferenceText(),
                  style: TextStyle(
                    fontSize: 16,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 40),

                // Progress indicator
                LinearProgressIndicator(
                  value: _elapsedTimeInSeconds / (widget.timeoutMs / 1000),
                  backgroundColor:
                      isDarkMode ? Colors.grey[800] : Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
                const SizedBox(height: 40),

                // Cancel button
                ElevatedButton.icon(
                  onPressed: _cancelCall,
                  icon: const Icon(Icons.cancel),
                  label: const Text('Отменить поиск'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
