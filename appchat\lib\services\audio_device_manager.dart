import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';

/// A service that manages audio device selection for WebRTC calls.
///
/// This class provides platform-specific implementations for controlling
/// the audio output device (speaker vs earpiece) during calls.
class AudioDeviceManager {
  // Singleton instance
  static final AudioDeviceManager _instance = AudioDeviceManager._internal();

  // Factory constructor
  factory AudioDeviceManager() {
    return _instance;
  }

  // Internal constructor
  AudioDeviceManager._internal();

  /// Set the audio output to speaker or earpiece
  ///
  /// [enabled] - true to use speaker, false to use earpiece
  Future<void> setSpeakerEnabled(bool enabled) async {
    try {
      if (kIsWeb) {
        // Web implementation
        debugPrint('Speaker control not fully supported on web');
        return;
      }

      if (Platform.isAndroid || Platform.isIOS) {
        // Mobile implementation using WebRTC's built-in audio device handling
        // Note: In newer versions of flutter_webrtc, audio device selection
        // is handled differently. For now, we'll use a simplified approach.
        debugPrint('Audio output requested: ${enabled ? "speaker" : "earpiece"}');
        // TODO: Implement proper audio device selection for newer flutter_webrtc version
      } else if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
        // Desktop implementation
        // Note: On desktop, audio device selection is typically handled by the OS
        // We can add more specific implementations if needed
        debugPrint(
          'Speaker control requested on desktop: ${enabled ? "enabled" : "disabled"}',
        );
      } else {
        debugPrint('Speaker control not implemented for this platform');
      }
    } catch (e) {
      debugPrint('Error setting audio output device: $e');
    }
  }

  /// Get available audio output devices
  ///
  /// Returns a list of available audio output devices
  Future<List<MediaDeviceInfo>> getAudioOutputDevices() async {
    try {
      final devices = await navigator.mediaDevices.enumerateDevices();
      return devices.where((device) => device.kind == 'audiooutput').toList();
    } catch (e) {
      debugPrint('Error getting audio output devices: $e');
      return [];
    }
  }

  /// Select a specific audio output device by device ID
  ///
  /// [deviceId] - The ID of the device to select
  Future<void> selectAudioOutputDevice(String deviceId) async {
    try {
      if (kIsWeb) {
        // Web implementation using setSinkId (if supported by browser)
        debugPrint('Attempting to set audio output device on web: $deviceId');
        // This would require JavaScript interop in a real implementation
      } else if (Platform.isAndroid || Platform.isIOS) {
        // On mobile, we typically just use speaker vs earpiece
        // This is a more advanced implementation for specific device selection
        debugPrint(
          'Selecting specific audio device not fully implemented on mobile',
        );
      } else {
        debugPrint(
          'Selecting specific audio device not implemented for this platform',
        );
      }
    } catch (e) {
      debugPrint('Error selecting audio output device: $e');
    }
  }
}
