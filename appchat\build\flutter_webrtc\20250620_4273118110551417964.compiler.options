"-Xallow-no-source-files" "-classpath" "D:\\ChatApp\\appchat\\build\\flutter_webrtc\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f40f201959b0661bb0bbcdb8e58ead63\\transformed\\jetified-flutter_embedding_debug-1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a68521102d9ba8948f26c890c5233e49\\transformed\\jetified-android-125.6422.03-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\61e1e86dd1a7f52b911bbe9534f110fe\\transformed\\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a7616d138eb360c3ab14825b1065d24a\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\86c94a3dab2eaa2bd16acc4730030573\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f3003aa29893ca817cb9a22f8c17ea41\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c283fdea8b7946930ebed8ea18e8ea5a\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ad3067c5fb8ecd618f28a12be80307c2\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ea14c27abb2d7524bb434714bc5f3ef9\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\6e5706bbb5c9487dc561c1600a2a6dae\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d6e6d4873b3ed37b61becf2e51297c04\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5cfdb61697b42043a10c6f0893ca84f9\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1b3e7019ea2733c8d32880fe08d69d99\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\7dff38001e31a2903e9cce5215222a95\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\26bcd78ad28b25ec0f7af7dea08e83c3\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b2fcaa169d1f4e492f15729be397348b\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bf5bfefb31017fee4b1ffa7b4ca385dd\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\31dde2edd8e7b0e74391e35c7d71d7a0\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\15c4b03a90500d8c5349eeae25704c5d\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\599a953c98489deda9576848fcba8141\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b54bfb55ea31b68adea07c08eb6bcc6a\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c96d92183e6826d51e63107e632589fc\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d27c2b41b3cf0f9fec1d03001268bcbd\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b1721d56478c405a937be197ef8624ce\\transformed\\jetified-kotlinx-coroutines-android-1.7.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b7c25d33876d3110f9b4605a5201c1af\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\6a17e9482c0c33c27976418e2d5a9400\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2335e535cb6f93e8f5e12934d13b3de7\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\dbf2c6bd1bcc0a3e9078297327c5ec82\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c48f1a78fe7ab8ea80433c3282e93b7a\\transformed\\jetified-kotlin-stdlib-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9b4a50f11186504b2092b8664ea7a206\\transformed\\jetified-kotlin-stdlib-common-1.8.22.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\81e03eaa0e8290d5de3a800b9f8c4d77\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c2dcff0a4d8b2f3035b092a70ce86364\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\7fde56d9a936ccd0313c298db4c3ab3d\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\95f8a7842a1925d2828c18a5c53ce9d6\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-31\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\ChatApp\\appchat\\build\\flutter_webrtc\\tmp\\kotlin-classes\\debug" "-jvm-target" "1.8" "-module-name" "flutter_webrtc_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\AudioDeviceKind.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\AudioProcessingAdapter.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\AudioProcessingController.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\AudioSwitchManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\AudioUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\LocalAudioTrack.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\PlaybackSamplesReadyCallbackAdapter.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\audio\\RecordSamplesReadyCallbackAdapter.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\CameraEventsHandler.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\DataChannelObserver.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\FlutterRTCFrameCryptor.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\FlutterRTCVideoRenderer.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\FlutterWebRTCPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\GetUserMediaImpl.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\LocalTrack.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\MethodCallHandlerImpl.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\OrientationAwareScreenCapturer.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\PeerConnectionObserver.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\AudioChannel.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\AudioSamplesInterceptor.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\AudioTrackInterceptor.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\FrameCapturer.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\MediaRecorderImpl.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\OutputAudioSamplesInterceptor.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\record\\VideoFileRenderer.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\StateProvider.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\SurfaceTextureRenderer.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\AnyThreadResult.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\AnyThreadSink.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\Callback.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\ConstraintsArray.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\ConstraintsMap.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\EglUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\MediaConstraintsUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\ObjectType.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\PermissionUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\utils\\Utils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\camera\\CameraRegionUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\camera\\CameraUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\camera\\DeviceOrientationManager.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\camera\\Point.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\camera\\SdkCapabilityChecker.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\LocalVideoTrack.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\video\\VideoCapturerInfo.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\org\\webrtc\\audio\\WebRtcAudioTrackUtils.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\org\\webrtc\\Camera1Helper.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\org\\webrtc\\Camera2Helper.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\org\\webrtc\\video\\CustomVideoDecoderFactory.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\org\\webrtc\\video\\CustomVideoEncoderFactory.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_webrtc-0.14.1\\android\\src\\main\\java\\com\\cloudwebrtc\\webrtc\\SimulcastVideoEncoderFactoryWrapper.kt"