{"logs": [{"outputFile": "com.hamode.appchat.app-mergeDebugResources-51:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ab37722a56b48a618e91dc4d4ff760f9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,121", "endOffsets": "159,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2851,2960", "endColumns": "108,121", "endOffsets": "2955,3077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\82766f43c900f418c4a97a171253098c\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4807", "endColumns": "139", "endOffsets": "4942"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\04f854c86687d4911c82dc9952d1fb33\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sl\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,456,581,684,827,953,1063,1163,1317,1420,1583,1709,1857,2005,2071,2129", "endColumns": "101,160,124,102,142,125,109,99,153,102,162,125,147,147,65,57,79", "endOffsets": "294,455,580,683,826,952,1062,1162,1316,1419,1582,1708,1856,2004,2070,2128,2208"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3805,3911,4076,4205,4312,4459,4589,4703,4947,5105,5212,5379,5509,5661,5813,5883,5945", "endColumns": "105,164,128,106,146,129,113,103,157,106,166,129,151,151,69,61,83", "endOffsets": "3906,4071,4200,4307,4454,4584,4698,4802,5100,5207,5374,5504,5656,5808,5878,5940,6024"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5cbc467f2278e032b60487f8f70af125\\transformed\\preference-1.2.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,343,484,653,741", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "171,257,338,479,648,736,820"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6029,6206,6610,6691,7016,7185,7273", "endColumns": "70,85,80,140,168,87,83", "endOffsets": "6095,6287,6686,6827,7180,7268,7352"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\0a2895bad47a5ad7be3689da4d9dc4ce\\transformed\\appcompat-1.1.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,896,988,1081,1176,1270,1366,1460,1556,1656,1748,1840,1923,2031,2139,2239,2352,2460,2568,2751,2851", "endColumns": "111,101,107,86,102,118,80,78,91,92,94,93,95,93,95,99,91,91,82,107,107,99,112,107,107,182,99,82", "endOffsets": "212,314,422,509,612,731,812,891,983,1076,1171,1265,1361,1455,1551,1651,1743,1835,1918,2026,2134,2234,2347,2455,2563,2746,2846,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,896,988,1081,1176,1270,1366,1460,1556,1656,1748,1840,1923,2031,2139,2239,2352,2460,2568,2751,6832", "endColumns": "111,101,107,86,102,118,80,78,91,92,94,93,95,93,95,99,91,91,82,107,107,99,112,107,107,182,99,82", "endOffsets": "212,314,422,509,612,731,812,891,983,1076,1171,1265,1361,1455,1551,1651,1743,1835,1918,2026,2134,2234,2347,2455,2563,2746,2846,6910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9ecd58143158fe7544d1e8fee291fc86\\transformed\\core-1.13.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3082,3179,3281,3379,3483,3586,3688,6915", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3174,3276,3374,3478,3581,3683,3800,7011"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\70d9edd68c4ac01d9150f2de0a6ef51d\\transformed\\browser-1.4.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,265,377", "endColumns": "105,103,111,101", "endOffsets": "156,260,372,474"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6100,6292,6396,6508", "endColumns": "105,103,111,101", "endOffsets": "6201,6391,6503,6605"}}]}]}