import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'firebase_options.dart';
import 'wrapper.dart';
import 'services/auth_service.dart';
import 'services/call_service.dart';
import 'services/user_service.dart';
import 'utils/avatar_helper.dart';
import 'providers/user_settings_provider.dart';
import 'providers/theme_provider.dart';
import 'widgets/age_range_selector.dart';
import 'widgets/age_selector.dart';
import 'screens/call_screen.dart';
import 'widgets/animated_call_button.dart';
import 'screens/waiting_screen.dart';
import 'screens/webrtc_test_screen.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Debug print to check if main is running
  debugPrint('Starting app initialization');

  // Initialize Firebase with the configuration from firebase_options.dart
  try {
    // Initialize Firebase
    final FirebaseApp app = await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    debugPrint('Firebase initialized successfully with app: ${app.name}');

    // Explicitly set the database URL for the Realtime Database
    FirebaseDatabase.instance.databaseURL =
        'https://chatapp-709b9-default-rtdb.firebaseio.com';

    // Verify Firebase Auth is working
    final FirebaseAuth auth = FirebaseAuth.instance;
    debugPrint('Firebase Auth instance created successfully');

    // Check if there's a current user (just for debugging)
    final User? currentUser = auth.currentUser;
    debugPrint('Current user: ${currentUser?.uid ?? 'No user logged in'}');
  } catch (e) {
    debugPrint('Failed to initialize Firebase: $e');
    if (e is FirebaseException) {
      debugPrint('Firebase error code: ${e.code}');
      debugPrint('Firebase error message: ${e.message}');
    }
  }

  // Use the normal app flow
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    debugPrint('Building MyApp widget');

    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => UserSettingsProvider()),
        ChangeNotifierProvider(create: (context) => ThemeProvider()),
      ],
      child: Builder(
        builder: (context) {
          final userSettingsProvider = Provider.of<UserSettingsProvider>(
            context,
          );
          final themeProvider = Provider.of<ThemeProvider>(context);

          // Initialize theme based on user settings after the first build
          if (userSettingsProvider.isLoaded && !themeProvider.isInitialized) {
            // Use Future.microtask to avoid calling setState during build
            Future.microtask(() {
              themeProvider.setThemeMode(userSettingsProvider.appTheme);
              themeProvider.setInitialized();
            });
          }

          return MaterialApp(
            title: 'Чат рулетка Хамуди.me',
            debugShowCheckedModeBanner: false,
            themeMode: themeProvider.themeMode,
            theme: themeProvider.lightTheme,
            darkTheme: themeProvider.darkTheme,
            // Use the Wrapper to handle authentication
            home: const Wrapper(child: MainScreen()),
          );
        },
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

// Helper method to build the app bar with the logo and title
PreferredSizeWidget buildAppBar() {
  return PreferredSize(
    preferredSize: const Size.fromHeight(kToolbarHeight),
    child: Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return AppBar(
          title: Row(
            children: [
              // Stylized microphone logo with animation and gradient
              _StylizedMicLogo(),
              const SizedBox(width: 10),
              ShaderMask(
                shaderCallback:
                    (bounds) => const LinearGradient(
                      colors: [Color(0xFF4F46E5), Color(0xFFFB7185)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ).createShader(bounds),
                child: const Text(
                  'Чат рулетка Хамуди.me',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontFamily: 'Pacifico',
                    letterSpacing: 0.5,
                    shadows: [
                      Shadow(
                        color: Colors.black26,
                        offset: Offset(1, 1),
                        blurRadius: 2,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          foregroundColor: isDarkMode ? Colors.white : Colors.black,
          elevation: 2,
          shadowColor: Colors.black.withAlpha(26),
        );
      },
    ),
  );
}

// Stylized microphone logo with animation
class _StylizedMicLogo extends StatefulWidget {
  @override
  State<_StylizedMicLogo> createState() => _StylizedMicLogoState();
}

class _StylizedMicLogoState extends State<_StylizedMicLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    final isDarkMode = themeProvider.isDarkMode;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF2A2A2A) : Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF4F46E5).withAlpha(isDarkMode ? 70 : 51),
                blurRadius: 8 * _pulseAnimation.value,
                spreadRadius: 1 * _pulseAnimation.value,
              ),
            ],
          ),
          child: Center(
            child: ShaderMask(
              shaderCallback:
                  (bounds) => const LinearGradient(
                    colors: [Color(0xFF4F46E5), Color(0xFFFB7185)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds),
              child: Icon(
                Icons.mic,
                color: Colors.white,
                size: 24 * _pulseAnimation.value,
              ),
            ),
          ),
        );
      },
    );
  }
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    // Initialize the user settings provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        Provider.of<UserSettingsProvider>(
          context,
          listen: false,
        ).loadSettings();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _currentIndex == 0 ? const CallsPage() : const SettingsPage(),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.phone), label: 'Звонки'),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'Настройки',
          ),
        ],
      ),
    );
  }
}

class CallsPage extends StatefulWidget {
  const CallsPage({super.key});

  @override
  State<CallsPage> createState() => _CallsPageState();
}

class _CallsPageState extends State<CallsPage> {
  final _callService = CallService();

  bool _isLoading = false;
  bool _isConnecting = false;
  List<Map<String, dynamic>> _recentCalls = [];

  @override
  void initState() {
    super.initState();
    // Load recent calls
    _loadRecentCalls();
  }

  // Get user settings provider directly when needed

  Future<void> _loadRecentCalls() async {
    setState(() {
      _isLoading = true;
    });
    try {
      List<Map<String, dynamic>> calls = await _callService.getRecentCalls();
      setState(() {
        _recentCalls = calls;
        _isLoading = false;
      });
    } catch (e) {
      // Error loading recent calls: $e
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _updatePreferences({
    String? gender,
    String? region,
    String? language,
    RangeValues? ageRange,
    int? age,
  }) async {
    try {
      // Use the provider to update preferences
      await Provider.of<UserSettingsProvider>(
        context,
        listen: false,
      ).updatePreferences(
        gender: gender,
        region: region,
        language: language,
        ageRange: ageRange,
        age: age,
      );
    } catch (e) {
      if (!mounted) return;
      // Show error to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to update preferences: $e')),
      );
    }
  }

  Future<void> _startRandomCall() async {
    setState(() {
      _isConnecting = true;
    });

    try {
      // Use the new method with hardcoded defaults
      debugPrint('Using startRandomCallWithDefaults method');

      // Call the new method that has hardcoded values
      Map<String, dynamic> result =
          await _callService.startRandomCallWithDefaults();

      if (!mounted) return;

      setState(() {
        _isConnecting = false;
      });

      if (result['success'] == true) {
        if (result['status'] == 'connected') {
          // A match was found immediately, navigate to call screen
          _navigateToCallScreen(
            result['callId'],
            result['matchedUserId'],
            true, // Caller is the initiator
          );
        } else if (result['status'] == 'waiting') {
          // No match found yet, navigate to waiting screen
          Navigator.of(context).push(
            MaterialPageRoute(
              builder:
                  (context) => WaitingScreen(
                    queueKey: result['queueKey'],
                    preferences: {
                      'gender': 'any',
                      'region': 'Russia',
                      'language': 'Russian',
                    },
                    timeoutMs: result['timeoutMs'] ?? 120000,
                  ),
            ),
          );
        }
      } else {
        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(result['error'] ?? 'Failed to start call')),
        );
      }
    } catch (e) {
      if (!mounted) return;

      setState(() {
        _isConnecting = false;
      });

      // Show error message
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Error starting call: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        children: [
          // Profile Section
          const SizedBox(height: 20),
          _buildProfileSection(),
          const SizedBox(height: 30),

          // Call Button
          _buildCallButton(context),
          const SizedBox(height: 30),

          // Call Preferences
          _buildCallPreferences(),
          const SizedBox(height: 20),

          // Recent Calls
          _buildRecentCalls(),
          const SizedBox(height: 20),

          // Test Panel removed
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    // Get the current gender from the provider
    final provider = Provider.of<UserSettingsProvider>(context);
    String preferredGender = provider.preferredGender;
    int userAge = provider.userAge;

    // Get avatar asset path based on selected gender
    String avatarAsset = AvatarHelper.getAvatarAsset(preferredGender);

    return Column(
      children: [
        Stack(
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(60),
                child: Image.asset(
                  avatarAsset,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return const Icon(Icons.person, size: 60);
                  },
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              child: Container(
                width: 30,
                height: 30,
                decoration: BoxDecoration(
                  color: Colors.green[500],
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: const Icon(Icons.phone, color: Colors.white, size: 16),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          preferredGender == 'male'
              ? 'Мужской пользователь'
              : preferredGender == 'female'
              ? 'Женский пользователь'
              : 'Анонимный пользователь',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 4),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'Возраст: $userAge лет',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(width: 4),
            GestureDetector(
              onTap: () {
                _showAgeSelectionDialog(context, userAge);
              },
              child: Icon(
                Icons.edit,
                size: 14,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          'Готов к подключению',
          style: TextStyle(fontSize: 14, color: Colors.grey[600]),
        ),
      ],
    );
  }

  void _showAgeSelectionDialog(BuildContext context, int currentAge) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Выберите ваш возраст',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  const SizedBox(height: 16),
                  AgeSelector(
                    initialAge: currentAge,
                    onChanged: (age) {
                      _updatePreferences(age: age);
                    },
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: const Text(
                      'Готово',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Widget _buildCallButton(BuildContext context) {
    return Column(
      children: [
        // Test mode indicator
        if (_callService.isTestModeEnabled)
          Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.amber[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.amber[700]!),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.science, size: 16, color: Colors.amber[800]),
                const SizedBox(width: 4),
                Text(
                  'Тестовый режим включен',
                  style: TextStyle(
                    color: Colors.amber[800],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        AnimatedCallButton(
          isLoading: _isConnecting,
          onPressed: _startRandomCall,
          loadingText: 'Поиск собеседника...',
          buttonText: 'Начать случайный звонок',
        ),
      ],
    );
  }

  Widget _buildCallPreferences() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Настройки звонка',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 16),

          // Gender selection
          Text('Пол', style: TextStyle(fontSize: 14, color: Colors.grey[600])),
          const SizedBox(height: 8),
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return Row(
                children: [
                  _buildGenderButton(
                    'Любой',
                    provider.preferredGender == 'any',
                    () {
                      _updatePreferences(gender: 'any');
                    },
                  ),
                  const SizedBox(width: 8),
                  _buildGenderButton(
                    'Мужской',
                    provider.preferredGender == 'male',
                    () {
                      _updatePreferences(gender: 'male');
                    },
                  ),
                  const SizedBox(width: 8),
                  _buildGenderButton(
                    'Женский',
                    provider.preferredGender == 'female',
                    () {
                      _updatePreferences(gender: 'female');
                    },
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),

          // Region selection
          Text(
            'Region',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF4F46E5).withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButton<String>(
                  value: provider.preferredRegion,
                  isExpanded: true,
                  underline: Container(),
                  icon: const Icon(
                    Icons.arrow_drop_down,
                    color: Color(0xFF4F46E5),
                  ),
                  style: const TextStyle(
                    color: Color(0xFF4F46E5),
                    fontSize: 14,
                  ),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      _updatePreferences(region: newValue);
                    }
                  },
                  items:
                      <String>[
                        'Russia',
                        'USA',
                        'Europe',
                        'Asia',
                        'Other',
                      ].map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                ),
              );
            },
          ),
          const SizedBox(height: 16),

          // Language selection
          Text(
            'Language',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF4F46E5).withAlpha(26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButton<String>(
                  value: provider.preferredLanguage,
                  isExpanded: true,
                  underline: Container(),
                  icon: const Icon(Icons.translate, color: Color(0xFF4F46E5)),
                  style: const TextStyle(
                    color: Color(0xFF4F46E5),
                    fontSize: 14,
                  ),
                  onChanged: (String? newValue) {
                    if (newValue != null) {
                      _updatePreferences(language: newValue);
                    }
                  },
                  items:
                      <String>[
                        'Russian',
                        'English',
                        'Spanish',
                        'French',
                        'German',
                      ].map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                ),
              );
            },
          ),
          const SizedBox(height: 16),

          // Age range selection with custom widget
          const SizedBox(height: 8),
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return AgeRangeSelector(
                initialValues: provider.preferredAgeRange,
                onChanged: (values) {
                  _updatePreferences(ageRange: values);
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildGenderButton(String text, bool isSelected, VoidCallback onTap) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 8),
          decoration: BoxDecoration(
            color:
                isSelected
                    ? const Color(0xFF4F46E5).withAlpha(26)
                    : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          alignment: Alignment.center,
          child: Text(
            text,
            style: TextStyle(
              color: isSelected ? const Color(0xFF4F46E5) : Colors.grey[600],
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecentCalls() {
    return Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 20 : 13),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Calls',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  GestureDetector(
                    onTap: _loadRecentCalls,
                    child: Text(
                      'Refresh',
                      style: TextStyle(
                        fontSize: 12,
                        color: const Color(0xFF4F46E5),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _recentCalls.isEmpty
                  ? Center(
                    child: Text(
                      'No recent calls',
                      style: TextStyle(
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      ),
                    ),
                  )
                  : Column(
                    children:
                        _recentCalls.map((call) {
                          // Format the timestamp
                          DateTime timestamp;
                          if (call['timestamp'] != null) {
                            if (call['timestamp'] is int) {
                              // Handle timestamp as milliseconds since epoch
                              timestamp = DateTime.fromMillisecondsSinceEpoch(
                                call['timestamp'],
                              );
                            } else if (call['timestamp'] is Map) {
                              // Handle Firestore timestamp in web
                              final seconds = call['timestamp']['seconds'] ?? 0;
                              final nanoseconds =
                                  call['timestamp']['nanoseconds'] ?? 0;
                              timestamp = DateTime.fromMillisecondsSinceEpoch(
                                seconds * 1000 +
                                    (nanoseconds / 1000000).round(),
                              );
                            } else {
                              try {
                                // Try to use toDate() if available
                                timestamp =
                                    (call['timestamp'] as dynamic).toDate();
                              } catch (e) {
                                // Fallback to current time
                                timestamp = DateTime.now();
                              }
                            }
                          } else {
                            timestamp = DateTime.now();
                          }
                          String formattedTime = _formatCallTime(
                            timestamp,
                            call['duration'],
                          );

                          // Get gender from other user's profile if available
                          String gender = 'any';
                          if (call['otherUserProfile'] != null &&
                              call['otherUserProfile']['preferredGender'] !=
                                  null) {
                            gender =
                                call['otherUserProfile']['preferredGender'];
                          }

                          return _buildRecentCallItem(
                            formattedTime,
                            gender: gender,
                          );
                        }).toList(),
                  ),
            ],
          ),
        );
      },
    );
  }

  String _formatCallTime(DateTime timestamp, int? duration) {
    // Get the difference between now and the timestamp
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    String timeText = '';

    if (difference.inDays == 0) {
      timeText =
          'Today, ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      timeText =
          'Yesterday, ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      timeText = '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }

    if (duration != null) {
      final minutes = duration ~/ 60;
      final seconds = duration % 60;
      timeText += ' • $minutes:${seconds.toString().padLeft(2, '0')}';
    }

    return timeText;
  }

  Widget _buildRecentCallItem(String time, {String gender = 'any'}) {
    // Get avatar asset path based on gender
    String avatarAsset = AvatarHelper.getSmallAvatarAsset(gender);

    return Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color:
                      isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[100],
                  shape: BoxShape.circle,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: Image.asset(
                    avatarAsset,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.person,
                        size: 20,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      gender == 'male'
                          ? 'Мужской пользователь'
                          : gender == 'female'
                          ? 'Женский пользователь'
                          : 'Анонимный пользователь',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isDarkMode ? Colors.white : Colors.black,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      time,
                      style: TextStyle(
                        fontSize: 12,
                        color: isDarkMode ? Colors.grey[400] : Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color:
                      isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[100],
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.phone,
                  size: 16,
                  color: const Color(0xFF4F46E5),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // Navigate to call screen
  void _navigateToCallScreen(
    String callId,
    String remoteUserId,
    bool isInitiator,
  ) {
    // Use a post-frame callback to avoid BuildContext issues
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      Navigator.of(context)
          .push(
            MaterialPageRoute(
              builder:
                  (context) => CallScreen(
                    callId: callId,
                    remoteUserId: remoteUserId,
                    isInitiator: isInitiator,
                  ),
            ),
          )
          .then((_) {
            if (mounted) {
              _loadRecentCalls();
            }
          });
    });
  }
}

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _authService = AuthService();
  final _userService = UserService();

  // All user preferences are now managed by UserSettingsProvider

  @override
  void initState() {
    super.initState();
    // Ensure settings are loaded from the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // Make sure settings are loaded in the provider
        Provider.of<UserSettingsProvider>(
          context,
          listen: false,
        ).loadSettings();
      }
    });
  }

  Future<void> _signOut() async {
    try {
      await _authService.signOut();
      // The wrapper will handle redirecting to sign in
    } catch (e) {
      // Error signing out: $e
      // Show error to user
      if (!mounted) return;
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('Failed to sign out: $e')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: buildAppBar(), // Use the consistent app bar
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
        children: [
          // Profile Section
          _buildProfileSection(),
          const SizedBox(height: 20),

          // Call Settings
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return _buildSettingsSection('Настройки звонков', [
                _buildSettingItem(
                  Icons.volume_up,
                  'Качество звука',
                  provider.soundQuality,
                  onTap: () {
                    _showOptionsDialog(
                      'Качество звука',
                      UserService.soundQualityOptions,
                      provider.soundQuality,
                      (value) async {
                        await provider.updateSoundQuality(value);
                      },
                    );
                  },
                ),
                _buildSettingItem(
                  Icons.language,
                  'Настройки соединения',
                  provider.connectionSetting,
                  onTap: () {
                    _showOptionsDialog(
                      'Настройки соединения',
                      UserService.connectionOptions,
                      provider.connectionSetting,
                      (value) async {
                        await provider.updateConnectionSettings(value);
                      },
                    );
                  },
                ),
              ]);
            },
          ),
          const SizedBox(height: 20),

          // Privacy Settings
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return _buildSettingsSection('Настройки конфиденциальности', [
                _buildSettingItem(
                  Icons.shield,
                  'Видимость профиля',
                  provider.profileVisibility,
                  onTap: () {
                    _showOptionsDialog(
                      'Видимость профиля',
                      UserService.visibilityOptions,
                      provider.profileVisibility,
                      (value) async {
                        await provider.updateProfileVisibility(value);
                      },
                    );
                  },
                ),
                _buildSettingItem(
                  Icons.person_off,
                  'Заблокированные',
                  null,
                  onTap: () {
                    _showBlockedUsersDialog();
                  },
                ),
                _buildSettingItem(
                  Icons.description,
                  'Политика конфиденциальности',
                  null,
                  onTap: () {
                    _showPrivacyPolicyDialog();
                  },
                ),
              ]);
            },
          ),
          const SizedBox(height: 20),

          // App Settings
          Consumer<UserSettingsProvider>(
            builder: (context, provider, child) {
              return _buildSettingsSection('Настройки приложения', [
                _buildSettingItemWithToggle(
                  Icons.notifications,
                  'Уведомления',
                  provider.notificationsEnabled,
                  (value) {
                    provider.updateNotificationSettings(value);
                  },
                ),
                _buildSettingItem(
                  Icons.translate,
                  'Language',
                  provider.appLanguage,
                  onTap: () {
                    _showOptionsDialog(
                      'Language',
                      UserService.languageOptions,
                      provider.appLanguage,
                      (value) async {
                        await provider.updateAppLanguage(value);
                      },
                    );
                  },
                ),
                _buildSettingItem(
                  Icons.brightness_medium,
                  'Тема',
                  provider.appTheme,
                  onTap: () {
                    // Get the ThemeProvider before the async gap
                    final themeProvider = Provider.of<ThemeProvider>(
                      context,
                      listen: false,
                    );

                    _showOptionsDialog(
                      'Тема',
                      UserService.themeOptions,
                      provider.appTheme,
                      (value) async {
                        // Update the theme in the UserSettingsProvider
                        await provider.updateAppTheme(value);

                        // Also update the ThemeProvider to apply the theme immediately
                        if (mounted) {
                          themeProvider.setThemeMode(value);
                        }
                      },
                    );
                  },
                ),

                // WebRTC Test option for debugging
                _buildSettingItem(
                  Icons.videocam,
                  'WebRTC Test',
                  'Debug',
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const WebRTCTestScreenWrapper(),
                      ),
                    );
                  },
                ),

                _buildSettingItem(
                  Icons.info,
                  'App Version',
                  '1.0.0',
                  isVersion: true,
                ),
              ]);
            },
          ),
          const SizedBox(height: 20),

          // Sign Out Button
          ElevatedButton(
            onPressed: _signOut,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              'Sign Out',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    // Use Consumer to get the latest user name from the provider
    // This avoids the flash of default name before settings are loaded
    return Consumer<UserSettingsProvider>(
      builder: (context, provider, child) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return GestureDetector(
          onTap: () {
            _showEditProfileDialog();
          },
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(isDarkMode ? 20 : 13),
                  blurRadius: 10,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 64,
                  height: 64,
                  decoration: BoxDecoration(
                    color:
                        isDarkMode ? const Color(0xFF2A2A2A) : Colors.grey[100],
                    shape: BoxShape.circle,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(32),
                    child: Image.asset(
                      AvatarHelper.getMediumAvatarAsset(
                        provider.preferredGender,
                      ),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Icon(
                          Icons.person,
                          size: 32,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[600],
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider
                            .userName, // Use the name from the provider directly
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Tap to edit profile',
                        style: TextStyle(
                          fontSize: 14,
                          color:
                              isDarkMode ? Colors.grey[400] : Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.chevron_right,
                  color: isDarkMode ? Colors.grey[400] : Colors.grey,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingsSection(String title, List<Widget> items) {
    return Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(isDarkMode ? 20 : 13),
                blurRadius: 10,
                spreadRadius: 0,
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
              ),
              const SizedBox(height: 16),
              ...items,
            ],
          ),
        );
      },
    );
  }

  Widget _buildSettingItem(
    IconData icon,
    String title,
    String? subtitle, {
    bool isVersion = false,
    VoidCallback? onTap,
  }) {
    return Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: const Color(
                      0xFF4F46E5,
                    ).withAlpha(isDarkMode ? 40 : 26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, size: 18, color: const Color(0xFF4F46E5)),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 14,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                ),
                if (subtitle != null)
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: isDarkMode ? Colors.grey[400] : Colors.grey[500],
                    ),
                  ),
                if (!isVersion && subtitle != null) const SizedBox(width: 8),
                if (!isVersion)
                  Icon(
                    Icons.chevron_right,
                    size: 16,
                    color: isDarkMode ? Colors.grey[400] : Colors.grey,
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildSettingItemWithToggle(
    IconData icon,
    String title,
    bool isEnabled,
    Function(bool) onChanged,
  ) {
    return Builder(
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: const Color(
                    0xFF4F46E5,
                  ).withAlpha(isDarkMode ? 40 : 26),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: const Color(0xFF4F46E5)),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: isDarkMode ? Colors.white : Colors.black,
                  ),
                ),
              ),
              Switch(
                value: isEnabled,
                onChanged: onChanged,
                activeColor: const Color(0xFF4F46E5),
              ),
            ],
          ),
        );
      },
    );
  }

  // Show options dialog for settings
  void _showOptionsDialog(
    String title,
    List<String> options,
    String currentValue,
    Function(String) onSelected,
  ) {
    showDialog(
      context: context,
      builder: (context) {
        final themeProvider = Provider.of<ThemeProvider>(context);
        final isDarkMode = themeProvider.isDarkMode;

        return AlertDialog(
          backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
          title: Text(
            title,
            style: TextStyle(color: isDarkMode ? Colors.white : Colors.black),
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: options.length,
              itemBuilder: (context, index) {
                final option = options[index];
                return ListTile(
                  title: Text(
                    option,
                    style: TextStyle(
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ),
                  trailing:
                      option == currentValue
                          ? const Icon(Icons.check, color: Color(0xFF4F46E5))
                          : null,
                  onTap: () {
                    onSelected(option);
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Отмена',
                style: TextStyle(
                  color: isDarkMode ? Colors.lightBlue : Colors.blue,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Show blocked users dialog
  void _showBlockedUsersDialog() async {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => const AlertDialog(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('Загрузка заблокированных пользователей...'),
              ],
            ),
          ),
    );

    try {
      List<Map<String, dynamic>> blockedUsers =
          await _userService.getBlockedUsers();

      if (!mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      if (blockedUsers.isEmpty) {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Заблокированные пользователи'),
                content: const Text('У вас нет заблокированных пользователей.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('OK'),
                  ),
                ],
              ),
        );
      } else {
        showDialog(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Заблокированные пользователи'),
                content: SizedBox(
                  width: double.maxFinite,
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: blockedUsers.length,
                    itemBuilder: (context, index) {
                      final user = blockedUsers[index];
                      final profile =
                          user['profile'] as Map<String, dynamic>? ?? {};
                      final displayName =
                          profile['displayName'] ?? 'Anonymous User';

                      return ListTile(
                        title: Text(displayName),
                        trailing: TextButton(
                          onPressed: () async {
                            await _userService.unblockUser(user['userId']);
                            if (!mounted) return;
                            // Use a local function to handle UI updates after async operation
                            void updateUI() {
                              Navigator.of(context).pop();
                              _showBlockedUsersDialog(); // Refresh the dialog
                            }

                            updateUI();
                          },
                          child: const Text('Разблокировать'),
                        ),
                      );
                    },
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Закрыть'),
                  ),
                ],
              ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      Navigator.of(context).pop(); // Close loading dialog

      // Show error dialog
      showDialog(
        context: context,
        builder:
            (context) => AlertDialog(
              title: const Text('Ошибка'),
              content: Text(
                'Ошибка загрузки заблокированных пользователей: $e',
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('OK'),
                ),
              ],
            ),
      );
    }
  }

  // Show privacy policy dialog
  void _showPrivacyPolicyDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Политика конфиденциальности'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Политика конфиденциальности Чат рулетка Хамуди.me',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('Последнее обновление: 1 мая 2023 г.'),
                  const SizedBox(height: 16),
                  const Text(
                    'Мы уважаем вашу конфиденциальность и обязуемся защищать ваши личные данные. Эта политика конфиденциальности описывает, какие данные мы собираем и как мы их используем.',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '1. Информация, которую мы собираем',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('a) Информация, которую вы предоставляете:'),
                  const Text(
                    '- Информация профиля пользователя (отображаемое имя, предпочтения по полу)',
                  ),
                  const Text(
                    '- Информация для аутентификации (электронная почта, если вы выбираете вход по электронной почте)',
                  ),
                  const Text(
                    '- Голосовые данные во время звонков (не хранятся постоянно)',
                  ),
                  const Text('- Настройки и предпочтения приложения'),
                  const SizedBox(height: 8),
                  const Text('b) Информация, собираемая автоматически:'),
                  const Text(
                    '- Информация об устройстве (модель, операционная система)',
                  ),
                  const Text('- Статистика использования приложения'),
                  const Text('- Информация о подключении'),
                  const Text('- Данные Firebase Analytics'),
                  const SizedBox(height: 16),
                  const Text(
                    '2. Как мы используем вашу информацию',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('Мы используем собранную информацию для:'),
                  const Text(
                    '- Предоставления и поддержания функциональности приложения',
                  ),
                  const Text(
                    '- Соединения вас с другими пользователями для голосовых звонков',
                  ),
                  const Text('- Улучшения и оптимизации работы приложения'),
                  const Text(
                    '- Обеспечения безопасности и предотвращения злоупотреблений',
                  ),
                  const Text('- Соблюдения юридических обязательств'),
                  const SizedBox(height: 16),
                  const Text(
                    '3. Обмен информацией',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Мы не продаем вашу личную информацию. Мы можем делиться информацией:',
                  ),
                  const Text(
                    '- С другими пользователями (ограничено необходимым для работы сервиса)',
                  ),
                  const Text('- С поставщиками услуг (Firebase, Google)'),
                  const Text(
                    '- Если это требуется по закону или для защиты прав',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Собираемые данные:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '- Информация профиля: имя, фотография (по желанию)',
                  ),
                  const Text(
                    '- Настройки: предпочтения поиска, настройки приложения',
                  ),
                  const Text('- Данные о звонках: длительность, время'),
                  const SizedBox(height: 16),
                  const Text(
                    'Использование данных:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('- Для предоставления и улучшения услуг'),
                  const Text('- Для подбора подходящих собеседников'),
                  const Text('- Для обеспечения безопасности платформы'),
                  const Text(
                    '4. Хранение и безопасность данных',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Мы используем стандартные отраслевые меры безопасности для защиты вашей информации.',
                  ),
                  const Text(
                    'Данные хранятся с использованием сервисов Google Firebase с соответствующими мерами контроля безопасности.',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '5. Ваш выбор',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('Вы можете:'),
                  const Text(
                    '- Обновлять или удалять информацию своей учетной записи',
                  ),
                  const Text(
                    '- Настраивать параметры конфиденциальности в приложении',
                  ),
                  const Text('- Отказаться от определенного сбора данных'),
                  const Text('- Запросить удаление ваших данных'),
                  const SizedBox(height: 16),
                  const Text(
                    '6. Конфиденциальность детей',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Наше приложение не предназначено для детей младше 13 лет, и мы сознательно не собираем информацию от детей младше 13 лет.',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '7. Изменения в этой политике',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Мы можем время от времени обновлять эту Политику конфиденциальности. Мы уведомим вас о любых изменениях, разместив новую Политику конфиденциальности на этой странице и обновив дату вступления в силу.',
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '8. Свяжитесь с нами',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Если у вас есть вопросы об этой Политике конфиденциальности, пожалуйста, свяжитесь с нами по адресу:',
                  ),
                  const Text('<EMAIL>'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  // Show edit profile dialog
  void _showEditProfileDialog() {
    // Get the current user name from the provider
    final provider = Provider.of<UserSettingsProvider>(context, listen: false);
    final TextEditingController nameController = TextEditingController(
      text: provider.userName,
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Редактировать профиль'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    labelText: 'Имя',
                    border: OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 16),
                const Text('Фото профиля будет добавлено в будущих версиях'),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Отмена'),
              ),
              TextButton(
                onPressed: () async {
                  final newName = nameController.text.trim();
                  if (newName.isNotEmpty) {
                    try {
                      // Update the profile using the provider
                      await provider.updateUserProfile(newName, null);

                      if (!mounted) return;
                      // Use a local function to handle UI updates after async operation
                      void updateUI() {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Профиль обновлен')),
                        );
                      }

                      updateUI();
                    } catch (e) {
                      if (!mounted) return;
                      // Use a local function to handle UI updates after async operation
                      void updateUI() {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Ошибка обновления профиля: $e'),
                          ),
                        );
                      }

                      updateUI();
                    }
                  }
                },
                child: const Text('Сохранить'),
              ),
            ],
          ),
    );
  }
}
