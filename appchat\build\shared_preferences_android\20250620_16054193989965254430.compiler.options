"-Xallow-no-source-files" "-classpath" "D:\\ChatApp\\appchat\\build\\shared_preferences_android\\intermediates\\compile_r_class_jar\\debug\\generateDebugRFile\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f40f201959b0661bb0bbcdb8e58ead63\\transformed\\jetified-flutter_embedding_debug-1.0.0-18b71d647a292a980abb405ac7d16fe1f0b20434.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\539b7b254fac116e5ac1bbf773614cbd\\transformed\\preference-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\fc02b7ffaf3c8c264c254273c10f0810\\transformed\\appcompat-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9e74c6159fb89cb27e7dd469de9687ca\\transformed\\jetified-fragment-ktx-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a7616d138eb360c3ab14825b1065d24a\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9ea733aef98b044cb3419d9106b739d7\\transformed\\recyclerview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5bc1f1a1a039a8e006c7738b4ca110e1\\transformed\\jetified-activity-ktx-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\86c94a3dab2eaa2bd16acc4730030573\\transformed\\jetified-activity-1.8.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1022f499a26d43a0c82e40feac0e5bfa\\transformed\\legacy-support-core-ui-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\8eace3591fb579a3f749552ab474d369\\transformed\\legacy-support-core-utils-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f3003aa29893ca817cb9a22f8c17ea41\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ad3067c5fb8ecd618f28a12be80307c2\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c283fdea8b7946930ebed8ea18e8ea5a\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f62d72b0695161cb738f347e58b72327\\transformed\\jetified-lifecycle-viewmodel-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\636648c9624779175a23b9ec7a723d2d\\transformed\\jetified-lifecycle-runtime-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ea14c27abb2d7524bb434714bc5f3ef9\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\6e5706bbb5c9487dc561c1600a2a6dae\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\d6e6d4873b3ed37b61becf2e51297c04\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5cfdb61697b42043a10c6f0893ca84f9\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1b3e7019ea2733c8d32880fe08d69d99\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\aad3c94f7ec263d052bdea3ee195daf2\\transformed\\jetified-appcompat-resources-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\f8f602f573ae38aed4e2b00953ac8f3c\\transformed\\drawerlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\4485497872e1d1ad9caf61630c29068d\\transformed\\slidingpanelayout-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\030ccd4aabca1eaf9bd1ee8f3cafca01\\transformed\\coordinatorlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\959fe24b192c8c6a75235b873d0c627b\\transformed\\customview-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\761975ed38b5d79709fea66bf4ab60d4\\transformed\\vectordrawable-animated-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a3ddff9e351506b87f035b940470a504\\transformed\\vectordrawable-1.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\34ef355f6f95ecae8913f6c7adeef024\\transformed\\swiperefreshlayout-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\7de042d063afd824f9d1e27a97fefa58\\transformed\\asynclayoutinflater-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\26bcd78ad28b25ec0f7af7dea08e83c3\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b2fcaa169d1f4e492f15729be397348b\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\bf5bfefb31017fee4b1ffa7b4ca385dd\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c96d92183e6826d51e63107e632589fc\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b54bfb55ea31b68adea07c08eb6bcc6a\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c08fb27f763514465db4fc1d7e1cc162\\transformed\\jetified-datastore-preferences-core-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\89d8861d335a37a3e9650e242774cdb7\\transformed\\jetified-datastore-core-okio-jvm-1.1.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\88780d0f606808b5ccc8a5e84450edae\\transformed\\jetified-datastore-core-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\7690423d8225c66110714aae87b82864\\transformed\\jetified-datastore-preferences-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\461742720504da295ffad375f352935e\\transformed\\jetified-datastore-release-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\dbf2c6bd1bcc0a3e9078297327c5ec82\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\20ef931480e38b154d475fad5e8c44ec\\transformed\\jetified-savedstate-ktx-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\599a953c98489deda9576848fcba8141\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\31dde2edd8e7b0e74391e35c7d71d7a0\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\15c4b03a90500d8c5349eeae25704c5d\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5810ee24d55c6b54e377ce8201991cdc\\transformed\\jetified-collection-ktx-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.1.0\\1f27220b47669781457de0d600849a5de0e89909\\collection-1.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\739458fff26b92ad8927872cd77c11d1\\transformed\\cursoradapter-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b74a4af9795c6231597149c996536c19\\transformed\\interpolator-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\a99b3cb056dddfd5b9e9518f261e0bab\\transformed\\documentfile-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c73d00fef5e7dacd7842bed15be75d2a\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ab67fa90ec567300b2bff4c79ac36b15\\transformed\\print-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\2335e535cb6f93e8f5e12934d13b3de7\\transformed\\jetified-annotation-jvm-1.8.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9496717feb7fb6db7d84ea63ce7edb21\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\28e52acab87096b7795625209c4a9a2e\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\888efef0f4d1345b3d47098798d70949\\transformed\\jetified-okio-jvm-3.4.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\b7c25d33876d3110f9b4605a5201c1af\\transformed\\jetified-kotlin-stdlib-jdk8-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\6a17e9482c0c33c27976418e2d5a9400\\transformed\\jetified-kotlin-stdlib-jdk7-1.8.20.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\e5d369d62582e8a5349d8efe923bee68\\transformed\\jetified-kotlin-stdlib-2.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\81e03eaa0e8290d5de3a800b9f8c4d77\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\c2dcff0a4d8b2f3035b092a70ce86364\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\7fde56d9a936ccd0313c298db4c3ab3d\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\95f8a7842a1925d2828c18a5c53ce9d6\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\platforms\\android-35\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "D:\\ChatApp\\appchat\\build\\shared_preferences_android\\tmp\\kotlin-classes\\debug" "-jvm-target" "11" "-module-name" "shared_preferences_android_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\LegacySharedPreferencesPlugin.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\Messages.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\java\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesListEncoder.java" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\MessagesAsync.g.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\SharedPreferencesPlugin.kt" "C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\shared_preferences_android-2.4.10\\android\\src\\main\\kotlin\\io\\flutter\\plugins\\sharedpreferences\\StringListObjectInputStream.kt"