com.hamode.appchat:styleable/ViewStubCompat = 0x7f100045
com.hamode.appchat:styleable/View = 0x7f100043
com.hamode.appchat:styleable/TextAppearance = 0x7f100041
com.hamode.appchat:styleable/SwitchPreferenceCompat = 0x7f100040
com.hamode.appchat:styleable/SwitchCompat = 0x7f10003e
com.hamode.appchat:styleable/StateListDrawableItem = 0x7f10003d
com.hamode.appchat:styleable/SplitPlaceholderRule = 0x7f10003b
com.hamode.appchat:styleable/SplitPairRule = 0x7f10003a
com.hamode.appchat:styleable/SeekBarPreference = 0x7f100036
com.hamode.appchat:styleable/SearchView = 0x7f100035
com.hamode.appchat:styleable/RecyclerView = 0x7f100034
com.hamode.appchat:styleable/RecycleListView = 0x7f100033
com.hamode.appchat:styleable/PreferenceTheme = 0x7f100032
com.hamode.appchat:styleable/PreferenceFragmentCompat = 0x7f10002f
com.hamode.appchat:styleable/Preference = 0x7f10002d
com.hamode.appchat:styleable/PopupWindowBackgroundState = 0x7f10002c
com.hamode.appchat:styleable/PopupWindow = 0x7f10002b
com.hamode.appchat:styleable/MultiSelectListPreference = 0x7f10002a
com.hamode.appchat:styleable/MenuGroup = 0x7f100027
com.hamode.appchat:styleable/LoadingImageView = 0x7f100026
com.hamode.appchat:styleable/ListPreference = 0x7f100025
com.hamode.appchat:styleable/LinearLayoutCompat_Layout = 0x7f100023
com.hamode.appchat:styleable/GradientColorItem = 0x7f100021
com.hamode.appchat:styleable/Fragment = 0x7f10001e
com.hamode.appchat:styleable/FontFamilyFont = 0x7f10001d
com.hamode.appchat:styleable/FontFamily = 0x7f10001c
com.hamode.appchat:styleable/EditTextPreference = 0x7f10001b
com.hamode.appchat:styleable/DrawerArrowToggle = 0x7f10001a
com.hamode.appchat:styleable/DialogPreference = 0x7f100019
com.hamode.appchat:styleable/CoordinatorLayout = 0x7f100017
com.hamode.appchat:styleable/CompoundButton = 0x7f100016
com.hamode.appchat:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.hamode.appchat:styleable/CheckBoxPreference = 0x7f100014
com.hamode.appchat:styleable/AppCompatTheme = 0x7f100010
com.hamode.appchat:color/abc_search_url_text_pressed = 0x7f05000e
com.hamode.appchat:styleable/AppCompatImageView = 0x7f10000c
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0086
com.hamode.appchat:styleable/AlertDialog = 0x7f100008
com.hamode.appchat:styleable/ActivityFilter = 0x7f100006
com.hamode.appchat:attr/splitMinSmallestWidthDp = 0x7f03012e
com.hamode.appchat:styleable/ActionBarLayout = 0x7f100001
com.hamode.appchat:style/Widget.Compat.NotificationActionContainer = 0x7f0f017f
com.hamode.appchat:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0144
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Large = 0x7f0f0017
com.hamode.appchat:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f017e
com.hamode.appchat:id/action_bar_spinner = 0x7f08002b
com.hamode.appchat:style/Widget.AppCompat.Toolbar = 0x7f0f017d
com.hamode.appchat:style/Widget.AppCompat.TextView = 0x7f0f017b
com.hamode.appchat:style/Widget.AppCompat.Spinner.Underlined = 0x7f0f017a
com.hamode.appchat:styleable/ActionBar = 0x7f100000
com.hamode.appchat:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0f0179
com.hamode.appchat:style/Widget.AppCompat.Spinner = 0x7f0f0177
com.hamode.appchat:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f016f
com.hamode.appchat:style/Widget.AppCompat.ListView.Menu = 0x7f0f016a
com.hamode.appchat:id/icon_frame = 0x7f080073
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0f010c
com.hamode.appchat:style/Widget.AppCompat.ListMenuView = 0x7f0f0166
com.hamode.appchat:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0f0165
com.hamode.appchat:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0f015f
com.hamode.appchat:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0f015d
com.hamode.appchat:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0f00dd
com.hamode.appchat:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0f015c
com.hamode.appchat:attr/fontWeight = 0x7f0300aa
com.hamode.appchat:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0f015a
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0155
com.hamode.appchat:attr/listPreferredItemPaddingRight = 0x7f0300da
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0f0154
com.hamode.appchat:style/Widget.AppCompat.ListView.DropDown = 0x7f0f0169
com.hamode.appchat:attr/contentInsetLeft = 0x7f030067
com.hamode.appchat:style/Widget.AppCompat.ImageButton = 0x7f0f014f
com.hamode.appchat:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0f014c
com.hamode.appchat:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f014a
com.hamode.appchat:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f0149
com.hamode.appchat:dimen/compat_control_corner_material = 0x7f060054
com.hamode.appchat:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0148
com.hamode.appchat:style/Widget.AppCompat.Button = 0x7f0f0141
com.hamode.appchat:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0064
com.hamode.appchat:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0f011c
com.hamode.appchat:style/Widget.AppCompat.ActionMode = 0x7f0f013e
com.hamode.appchat:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0f013d
com.hamode.appchat:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0f013c
com.hamode.appchat:style/Widget.AppCompat.ActionButton = 0x7f0f013b
com.hamode.appchat:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f0134
com.hamode.appchat:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0f0138
com.hamode.appchat:style/ThemeOverlay.AppCompat.Dark = 0x7f0f012f
com.hamode.appchat:attr/ttcIndex = 0x7f030172
com.hamode.appchat:layout/abc_screen_simple = 0x7f0b0015
com.hamode.appchat:style/Theme.Hidden = 0x7f0f012b
com.hamode.appchat:id/expand_activities_button = 0x7f080065
com.hamode.appchat:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.hamode.appchat:style/Theme.AppCompat.Light.NoActionBar = 0x7f0f0129
com.hamode.appchat:attr/itemPadding = 0x7f0300bf
com.hamode.appchat:dimen/browser_actions_context_menu_min_padding = 0x7f06004f
com.hamode.appchat:styleable/AnimatedStateListDrawableCompat = 0x7f100009
com.hamode.appchat:style/Theme.AppCompat.Light.Dialog = 0x7f0f0125
com.hamode.appchat:dimen/abc_text_size_caption_material = 0x7f06003f
com.hamode.appchat:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0f0124
com.hamode.appchat:style/Theme.AppCompat.Light = 0x7f0f0123
com.hamode.appchat:style/Theme.AppCompat.DialogWhenLarge = 0x7f0f0122
com.hamode.appchat:id/adjacent = 0x7f08003b
com.hamode.appchat:drawable/ic_other_sign_in = 0x7f07007b
com.hamode.appchat:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0f0121
com.hamode.appchat:attr/imageAspectRatioAdjust = 0x7f0300b8
com.hamode.appchat:layout/select_dialog_item_material = 0x7f0b0039
com.hamode.appchat:style/Theme.AppCompat.Dialog.Alert = 0x7f0f0120
com.hamode.appchat:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0f012e
com.hamode.appchat:style/Theme.AppCompat.Dialog = 0x7f0f011f
com.hamode.appchat:id/topPanel = 0x7f0800d6
com.hamode.appchat:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0f011e
com.hamode.appchat:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0f0119
com.hamode.appchat:style/TextAppearance.Compat.Notification.Title = 0x7f0f0112
com.hamode.appchat:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f0115
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0109
com.hamode.appchat:styleable/PreferenceGroup = 0x7f100030
com.hamode.appchat:layout/abc_action_menu_layout = 0x7f0b0003
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0107
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0105
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0f0101
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f0100
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f00fe
com.hamode.appchat:style/TextAppearance.AppCompat.Tooltip = 0x7f0f00fa
com.hamode.appchat:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0f00f9
com.hamode.appchat:styleable/ActivityChooserView = 0x7f100005
com.hamode.appchat:style/TextAppearance.AppCompat.Title = 0x7f0f00f8
com.hamode.appchat:attr/drawableTopCompat = 0x7f030087
com.hamode.appchat:drawable/ic_call_answer_video_low = 0x7f070077
com.hamode.appchat:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0f00f3
com.hamode.appchat:style/TextAppearance.AppCompat.Menu = 0x7f0f00f1
com.hamode.appchat:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0f00f0
com.hamode.appchat:attr/textAllCaps = 0x7f03014a
com.hamode.appchat:attr/imageAspectRatio = 0x7f0300b7
com.hamode.appchat:string/search_menu_title = 0x7f0e0047
com.hamode.appchat:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f00ee
com.hamode.appchat:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f00ed
com.hamode.appchat:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0f00eb
com.hamode.appchat:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0f00ea
com.hamode.appchat:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.hamode.appchat:style/TextAppearance.AppCompat.Inverse = 0x7f0f00e8
com.hamode.appchat:style/TextAppearance.AppCompat.Display2 = 0x7f0f00e4
com.hamode.appchat:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0f0055
com.hamode.appchat:style/TextAppearance.AppCompat.Display1 = 0x7f0f00e3
com.hamode.appchat:style/TextAppearance.AppCompat.Body1 = 0x7f0f00df
com.hamode.appchat:color/abc_tint_edittext = 0x7f050014
com.hamode.appchat:styleable/Capability = 0x7f100013
com.hamode.appchat:style/TextAppearance.AppCompat = 0x7f0f00de
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0f00db
com.hamode.appchat:id/ALT = 0x7f080000
com.hamode.appchat:color/common_google_signin_btn_text_dark_default = 0x7f05002f
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0f00d9
com.hamode.appchat:attr/layout = 0x7f0300c4
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0f00d7
com.hamode.appchat:attr/orderingFromXml = 0x7f0300ec
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0f00d2
com.hamode.appchat:style/ThemeOverlay.AppCompat.DayNight = 0x7f0f0131
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0f00d0
com.hamode.appchat:color/material_deep_teal_200 = 0x7f050047
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0f00ce
com.hamode.appchat:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0f00cd
com.hamode.appchat:style/PreferenceThemeOverlay.v14 = 0x7f0f00cb
com.hamode.appchat:style/PreferenceSummaryTextStyle = 0x7f0f00c9
com.hamode.appchat:style/PreferenceFragmentList.Material = 0x7f0f00c8
com.hamode.appchat:style/PreferenceFragment.Material = 0x7f0f00c6
com.hamode.appchat:style/Preference.SwitchPreference.Material = 0x7f0f00c1
com.hamode.appchat:attr/tint = 0x7f03015e
com.hamode.appchat:styleable/ActionMenuItemView = 0x7f100002
com.hamode.appchat:style/Preference.SwitchPreference = 0x7f0f00c0
com.hamode.appchat:style/Preference.PreferenceScreen = 0x7f0f00bc
com.hamode.appchat:attr/subtitleTextStyle = 0x7f03013c
com.hamode.appchat:attr/reverseLayout = 0x7f030110
com.hamode.appchat:styleable/ActionMenuView = 0x7f100003
com.hamode.appchat:id/custom = 0x7f08005b
com.hamode.appchat:style/Preference.Information.Material = 0x7f0f00ba
com.hamode.appchat:style/Preference.Information = 0x7f0f00b9
com.hamode.appchat:style/Preference.DropDown.Material = 0x7f0f00b8
com.hamode.appchat:style/Preference.SwitchPreferenceCompat = 0x7f0f00c2
com.hamode.appchat:style/Base.Widget.AppCompat.Toolbar = 0x7f0f009f
com.hamode.appchat:style/Preference.DialogPreference.Material = 0x7f0f00b6
com.hamode.appchat:layout/abc_action_mode_bar = 0x7f0b0004
com.hamode.appchat:style/Preference.DialogPreference.EditTextPreference = 0x7f0f00b4
com.hamode.appchat:style/TextAppearance.Compat.Notification.Time = 0x7f0f0111
com.hamode.appchat:style/Preference.Category.Material = 0x7f0f00b0
com.hamode.appchat:style/Preference = 0x7f0f00ae
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0f004c
com.hamode.appchat:style/Platform.V25.AppCompat = 0x7f0f00ab
com.hamode.appchat:drawable/abc_ic_search_api_material = 0x7f070020
com.hamode.appchat:dimen/hint_alpha_material_dark = 0x7f06005f
com.hamode.appchat:style/Platform.V21.AppCompat = 0x7f0f00a9
com.hamode.appchat:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0f00a8
com.hamode.appchat:style/Platform.AppCompat.Light = 0x7f0f00a5
com.hamode.appchat:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0f00a0
com.hamode.appchat:style/Widget.AppCompat.Spinner.DropDown = 0x7f0f0178
com.hamode.appchat:style/Base.Widget.AppCompat.Spinner = 0x7f0f009b
com.hamode.appchat:style/Base.Widget.AppCompat.ActionButton = 0x7f0f006c
com.hamode.appchat:style/Base.Widget.AppCompat.SeekBar = 0x7f0f0099
com.hamode.appchat:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0f0098
com.hamode.appchat:dimen/abc_panel_menu_list_width = 0x7f060034
com.hamode.appchat:attr/collapseIcon = 0x7f030056
com.hamode.appchat:style/Base.Widget.AppCompat.SearchView = 0x7f0f0097
com.hamode.appchat:style/Base.Widget.AppCompat.RatingBar = 0x7f0f0094
com.hamode.appchat:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.hamode.appchat:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0f0090
com.hamode.appchat:style/Base.Widget.AppCompat.PopupMenu = 0x7f0f008f
com.hamode.appchat:style/TextAppearance.Compat.Notification.Line2 = 0x7f0f0110
com.hamode.appchat:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f00f7
com.hamode.appchat:style/Base.Widget.AppCompat.ListView = 0x7f0f008c
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0083
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0f0082
com.hamode.appchat:style/Base.Widget.AppCompat.ImageButton = 0x7f0f0081
com.hamode.appchat:attr/keylines = 0x7f0300c1
com.hamode.appchat:id/search_src_text = 0x7f0800aa
com.hamode.appchat:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0f007e
com.hamode.appchat:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0f0079
com.hamode.appchat:style/Base.Widget.AppCompat.ButtonBar = 0x7f0f0078
com.hamode.appchat:style/Base.Widget.AppCompat.Button.Small = 0x7f0f0077
com.hamode.appchat:dimen/tooltip_y_offset_non_touch = 0x7f060082
com.hamode.appchat:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0f0075
com.hamode.appchat:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0f0073
com.hamode.appchat:attr/tickMarkTintMode = 0x7f03015d
com.hamode.appchat:color/abc_primary_text_material_light = 0x7f05000b
com.hamode.appchat:style/Base.Widget.AppCompat.Button = 0x7f0f0072
com.hamode.appchat:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0f0071
com.hamode.appchat:style/Base.Widget.AppCompat.ActionMode = 0x7f0f006f
com.hamode.appchat:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0f006d
com.hamode.appchat:style/Theme.AppCompat.DayNight = 0x7f0f0118
com.hamode.appchat:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0f006a
com.hamode.appchat:id/accessibility_custom_action_7 = 0x7f080024
com.hamode.appchat:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0f0069
com.hamode.appchat:style/Base.Widget.AppCompat.ActionBar = 0x7f0f0067
com.hamode.appchat:style/Base.V28.Theme.AppCompat.Light = 0x7f0f005e
com.hamode.appchat:style/Base.V26.Theme.AppCompat.Light = 0x7f0f005b
com.hamode.appchat:style/Base.V23.Theme.AppCompat = 0x7f0f0058
com.hamode.appchat:string/common_google_play_services_install_title = 0x7f0e002a
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0f00cf
com.hamode.appchat:style/Base.V22.Theme.AppCompat.Light = 0x7f0f0057
com.hamode.appchat:styleable/GradientColor = 0x7f100020
com.hamode.appchat:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0f00dc
com.hamode.appchat:style/Base.V21.Theme.AppCompat.Light = 0x7f0f0053
com.hamode.appchat:style/Platform.AppCompat = 0x7f0f00a4
com.hamode.appchat:style/Base.V21.Theme.AppCompat = 0x7f0f0051
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0f004f
com.hamode.appchat:id/preferences_header = 0x7f080091
com.hamode.appchat:id/text2 = 0x7f0800ce
com.hamode.appchat:style/Widget.AppCompat.ButtonBar = 0x7f0f0147
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0f004e
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0f004b
com.hamode.appchat:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0f0052
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat = 0x7f0f004a
com.hamode.appchat:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0f0047
com.hamode.appchat:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0046
com.hamode.appchat:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0f0045
com.hamode.appchat:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0f0044
com.hamode.appchat:id/accessibility_custom_action_11 = 0x7f08000a
com.hamode.appchat:style/Base.Theme.AppCompat.Light = 0x7f0f0043
com.hamode.appchat:style/Widget.AppCompat.SeekBar = 0x7f0f0175
com.hamode.appchat:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0f0041
com.hamode.appchat:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0f0040
com.hamode.appchat:style/Base.V7.Theme.AppCompat.Light = 0x7f0f0061
com.hamode.appchat:style/Base.Theme.AppCompat.CompactMenu = 0x7f0f003d
com.hamode.appchat:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0f003b
com.hamode.appchat:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0f017c
com.hamode.appchat:dimen/preferences_detail_width = 0x7f06007a
com.hamode.appchat:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0039
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0f0037
com.hamode.appchat:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.hamode.appchat:styleable/ColorStateListItem = 0x7f100015
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f0036
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f0035
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0033
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0f0032
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0031
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f002e
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f002c
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0f002b
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0f0026
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0153
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0f0024
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0f0022
com.hamode.appchat:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0f0020
com.hamode.appchat:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.hamode.appchat:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f001f
com.hamode.appchat:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.hamode.appchat:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0f001e
com.hamode.appchat:dimen/abc_list_item_height_small_material = 0x7f060032
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0f001c
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Medium = 0x7f0f001b
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0f001a
com.hamode.appchat:attr/circleCrop = 0x7f030051
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0f00da
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0f0014
com.hamode.appchat:dimen/compat_button_padding_vertical_material = 0x7f060053
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0f0012
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0f0011
com.hamode.appchat:style/Base.V28.Theme.AppCompat = 0x7f0f005d
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Button = 0x7f0f000f
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0f000e
com.hamode.appchat:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0f000b
com.hamode.appchat:interpolator/fast_out_slow_in = 0x7f0a0006
com.hamode.appchat:style/Base.Animation.AppCompat.Dialog = 0x7f0f0007
com.hamode.appchat:style/Base.AlertDialog.AppCompat.Light = 0x7f0f0006
com.hamode.appchat:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0f0062
com.hamode.appchat:string/preference_copied = 0x7f0e0045
com.hamode.appchat:string/google_storage_bucket = 0x7f0e0043
com.hamode.appchat:string/google_app_id = 0x7f0e0041
com.hamode.appchat:attr/state_above_anchor = 0x7f030134
com.hamode.appchat:color/material_blue_grey_800 = 0x7f050044
com.hamode.appchat:string/gcm_defaultSenderId = 0x7f0e003f
com.hamode.appchat:color/primary_material_light = 0x7f050056
com.hamode.appchat:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0f006b
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0f002d
com.hamode.appchat:dimen/compat_notification_large_icon_max_height = 0x7f060055
com.hamode.appchat:string/abc_menu_ctrl_shortcut_label = 0x7f0e0009
com.hamode.appchat:string/firebase_database_url = 0x7f0e003e
com.hamode.appchat:layout/preference_dropdown = 0x7f0b002d
com.hamode.appchat:style/Widget.AppCompat.PopupMenu = 0x7f0f016b
com.hamode.appchat:string/fcm_fallback_notification_channel_label = 0x7f0e003d
com.hamode.appchat:string/fallback_menu_item_open_in_browser = 0x7f0e003b
com.hamode.appchat:string/expand_button_title = 0x7f0e0039
com.hamode.appchat:string/common_google_play_services_update_title = 0x7f0e0031
com.hamode.appchat:string/common_google_play_services_update_text = 0x7f0e0030
com.hamode.appchat:string/common_google_play_services_unsupported_text = 0x7f0e002e
com.hamode.appchat:string/common_google_play_services_unknown_issue = 0x7f0e002d
com.hamode.appchat:dimen/abc_text_size_display_1_material = 0x7f060040
com.hamode.appchat:string/common_google_play_services_notification_ticker = 0x7f0e002c
com.hamode.appchat:drawable/tooltip_frame_dark = 0x7f07008d
com.hamode.appchat:layout/browser_actions_context_menu_row = 0x7f0b001d
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0f0103
com.hamode.appchat:attr/queryBackground = 0x7f030109
com.hamode.appchat:string/common_google_play_services_enable_title = 0x7f0e0027
com.hamode.appchat:id/action_container = 0x7f08002e
com.hamode.appchat:string/common_google_play_services_enable_text = 0x7f0e0026
com.hamode.appchat:styleable/PreferenceImageView = 0x7f100031
com.hamode.appchat:attr/alphabeticModifiers = 0x7f03002e
com.hamode.appchat:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f0114
com.hamode.appchat:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0f011d
com.hamode.appchat:attr/drawableRightCompat = 0x7f030082
com.hamode.appchat:color/ripple_material_dark = 0x7f05005b
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f0029
com.hamode.appchat:string/call_notification_screening_text = 0x7f0e0024
com.hamode.appchat:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f070067
com.hamode.appchat:string/call_notification_ongoing_text = 0x7f0e0023
com.hamode.appchat:string/call_notification_hang_up_action = 0x7f0e0021
com.hamode.appchat:style/BasePreferenceThemeOverlay = 0x7f0f00a1
com.hamode.appchat:string/call_notification_decline_action = 0x7f0e0020
com.hamode.appchat:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0f007a
com.hamode.appchat:string/common_google_play_services_update_button = 0x7f0e002f
com.hamode.appchat:string/call_notification_answer_action = 0x7f0e001e
com.hamode.appchat:string/androidx_startup = 0x7f0e001d
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0f0158
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0f0023
com.hamode.appchat:string/androidx.credentials.TYPE_PUBLIC_KEY_CREDENTIAL = 0x7f0e001c
com.hamode.appchat:id/always = 0x7f080040
com.hamode.appchat:string/abc_shareactionprovider_share_with_application = 0x7f0e0019
com.hamode.appchat:string/abc_shareactionprovider_share_with = 0x7f0e0018
com.hamode.appchat:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.hamode.appchat:id/parentPanel = 0x7f08008e
com.hamode.appchat:id/accessibility_custom_action_3 = 0x7f08001e
com.hamode.appchat:string/abc_searchview_description_submit = 0x7f0e0016
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0f0050
com.hamode.appchat:string/abc_searchview_description_query = 0x7f0e0014
com.hamode.appchat:string/abc_menu_sym_shortcut_label = 0x7f0e0010
com.hamode.appchat:attr/switchPreferenceStyle = 0x7f030144
com.hamode.appchat:string/abc_menu_shift_shortcut_label = 0x7f0e000e
com.hamode.appchat:attr/finishPrimaryWithSecondary = 0x7f03009c
com.hamode.appchat:attr/actionModeSplitBackground = 0x7f03001a
com.hamode.appchat:drawable/common_google_signin_btn_icon_dark_normal = 0x7f070061
com.hamode.appchat:string/abc_menu_function_shortcut_label = 0x7f0e000c
com.hamode.appchat:string/abc_capital_off = 0x7f0e0006
com.hamode.appchat:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.hamode.appchat:string/abc_action_mode_done = 0x7f0e0003
com.hamode.appchat:string/abc_action_menu_overflow_description = 0x7f0e0002
com.hamode.appchat:id/tag_accessibility_actions = 0x7f0800c0
com.hamode.appchat:raw/firebase_common_keep = 0x7f0d0000
com.hamode.appchat:mipmap/ic_launcher = 0x7f0c0000
com.hamode.appchat:style/Widget.AppCompat.Button.Colored = 0x7f0f0145
com.hamode.appchat:layout/preference_recyclerview = 0x7f0b0033
com.hamode.appchat:attr/radioButtonStyle = 0x7f03010c
com.hamode.appchat:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0089
com.hamode.appchat:layout/preference_list_fragment = 0x7f0b0031
com.hamode.appchat:dimen/abc_text_size_menu_material = 0x7f060048
com.hamode.appchat:layout/preference_information = 0x7f0b002f
com.hamode.appchat:color/androidx_core_secondary_text_default_material_light = 0x7f05001b
com.hamode.appchat:attr/navigationIcon = 0x7f0300e6
com.hamode.appchat:style/Base.Widget.AppCompat.ProgressBar = 0x7f0f0092
com.hamode.appchat:drawable/abc_textfield_default_mtrl_alpha = 0x7f070051
com.hamode.appchat:layout/preference_dropdown_material = 0x7f0b002e
com.hamode.appchat:layout/preference_dialog_edittext = 0x7f0b002c
com.hamode.appchat:layout/preference = 0x7f0b0029
com.hamode.appchat:layout/notification_template_icon_group = 0x7f0b0026
com.hamode.appchat:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0f0140
com.hamode.appchat:layout/notification_template_custom_big = 0x7f0b0025
com.hamode.appchat:layout/notification_action_tombstone = 0x7f0b0024
com.hamode.appchat:layout/ime_base_split_test_activity = 0x7f0b0021
com.hamode.appchat:layout/custom_dialog = 0x7f0b001e
com.hamode.appchat:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0f003a
com.hamode.appchat:layout/abc_tooltip = 0x7f0b001b
com.hamode.appchat:layout/abc_search_view = 0x7f0b0019
com.hamode.appchat:layout/abc_screen_toolbar = 0x7f0b0017
com.hamode.appchat:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.hamode.appchat:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.hamode.appchat:id/fill = 0x7f080067
com.hamode.appchat:layout/abc_expanded_menu_layout = 0x7f0b000d
com.hamode.appchat:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.hamode.appchat:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.hamode.appchat:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0f0160
com.hamode.appchat:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.hamode.appchat:id/androidx_window_activity_scope = 0x7f080043
com.hamode.appchat:id/useLogo = 0x7f0800e0
com.hamode.appchat:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.hamode.appchat:style/Widget.AppCompat.ListView = 0x7f0f0168
com.hamode.appchat:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0f007b
com.hamode.appchat:integer/status_bar_notification_info_maxnum = 0x7f090007
com.hamode.appchat:integer/preferences_header_pane_weight = 0x7f090006
com.hamode.appchat:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.hamode.appchat:style/Preference.SwitchPreferenceCompat.Material = 0x7f0f00c3
com.hamode.appchat:drawable/common_google_signin_btn_text_light_focused = 0x7f07006e
com.hamode.appchat:integer/abc_config_activityShortDur = 0x7f090001
com.hamode.appchat:dimen/abc_text_size_button_material = 0x7f06003e
com.hamode.appchat:drawable/notification_action_background = 0x7f07007f
com.hamode.appchat:integer/abc_config_activityDefaultDur = 0x7f090000
com.hamode.appchat:id/wrap_content = 0x7f0800e8
com.hamode.appchat:id/withText = 0x7f0800e7
com.hamode.appchat:id/view_tree_view_model_store_owner = 0x7f0800e4
com.hamode.appchat:attr/nestedScrollViewStyle = 0x7f0300e9
com.hamode.appchat:layout/preference_widget_switch = 0x7f0b0037
com.hamode.appchat:id/uniform = 0x7f0800de
com.hamode.appchat:id/transition_scene_layoutid_cache = 0x7f0800db
com.hamode.appchat:attr/dividerVertical = 0x7f03007e
com.hamode.appchat:id/transition_position = 0x7f0800da
com.hamode.appchat:id/transition_layout_save = 0x7f0800d9
com.hamode.appchat:color/primary_dark_material_dark = 0x7f050053
com.hamode.appchat:id/transition_current_scene = 0x7f0800d8
com.hamode.appchat:style/Widget.AppCompat.ListPopupWindow = 0x7f0f0167
com.hamode.appchat:id/topToBottom = 0x7f0800d7
com.hamode.appchat:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0f0060
com.hamode.appchat:id/top = 0x7f0800d5
com.hamode.appchat:id/title = 0x7f0800d2
com.hamode.appchat:id/light = 0x7f08007c
com.hamode.appchat:style/Widget.AppCompat.Button.Small = 0x7f0f0146
com.hamode.appchat:string/abc_searchview_description_clear = 0x7f0e0013
com.hamode.appchat:style/Animation.AppCompat.DropDownUp = 0x7f0f0003
com.hamode.appchat:id/time = 0x7f0800d1
com.hamode.appchat:id/textSpacerNoButtons = 0x7f0800cf
com.hamode.appchat:attr/tooltipText = 0x7f03016e
com.hamode.appchat:id/tag_unhandled_key_listeners = 0x7f0800cb
com.hamode.appchat:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f070062
com.hamode.appchat:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000d
com.hamode.appchat:id/tag_unhandled_key_event_manager = 0x7f0800ca
com.hamode.appchat:id/tag_transition_group = 0x7f0800c9
com.hamode.appchat:id/ghost_view = 0x7f08006c
com.hamode.appchat:id/tag_state_description = 0x7f0800c8
com.hamode.appchat:id/tag_screen_reader_focusable = 0x7f0800c7
com.hamode.appchat:id/tag_on_receive_content_mime_types = 0x7f0800c6
com.hamode.appchat:dimen/abc_switch_padding = 0x7f06003b
com.hamode.appchat:string/abc_activity_chooser_view_see_all = 0x7f0e0004
com.hamode.appchat:id/tabMode = 0x7f0800bf
com.hamode.appchat:attr/dialogCornerRadius = 0x7f030071
com.hamode.appchat:id/switchWidget = 0x7f0800be
com.hamode.appchat:style/Platform.V25.AppCompat.Light = 0x7f0f00ac
com.hamode.appchat:id/submit_area = 0x7f0800bd
com.hamode.appchat:id/standard = 0x7f0800ba
com.hamode.appchat:style/ThemeOverlay.AppCompat = 0x7f0f012d
com.hamode.appchat:id/src_in = 0x7f0800b8
com.hamode.appchat:id/split_action_bar = 0x7f0800b6
com.hamode.appchat:style/TextAppearance.AppCompat.Display3 = 0x7f0f00e5
com.hamode.appchat:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0f009c
com.hamode.appchat:color/material_grey_800 = 0x7f05004d
com.hamode.appchat:string/common_google_play_services_enable_button = 0x7f0e0025
com.hamode.appchat:id/spinner = 0x7f0800b5
com.hamode.appchat:integer/config_tooltipAnimTime = 0x7f090003
com.hamode.appchat:id/collapseActionView = 0x7f080058
com.hamode.appchat:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0f00f5
com.hamode.appchat:attr/dialogPreferredPadding = 0x7f030076
com.hamode.appchat:id/spacer = 0x7f0800b3
com.hamode.appchat:dimen/fastscroll_default_thickness = 0x7f060059
com.hamode.appchat:id/showTitle = 0x7f0800b2
com.hamode.appchat:id/showHome = 0x7f0800b1
com.hamode.appchat:id/showCustom = 0x7f0800b0
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f002a
com.hamode.appchat:style/Preference.DropDown = 0x7f0f00b7
com.hamode.appchat:id/seekbar_value = 0x7f0800ad
com.hamode.appchat:id/search_voice_btn = 0x7f0800ab
com.hamode.appchat:attr/arrowShaftLength = 0x7f030032
com.hamode.appchat:id/search_plate = 0x7f0800a9
com.hamode.appchat:bool/config_materialPreferenceIconSpaceReserved = 0x7f040003
com.hamode.appchat:id/search_edit_frame = 0x7f0800a6
com.hamode.appchat:id/forever = 0x7f08006a
com.hamode.appchat:id/search_button = 0x7f0800a4
com.hamode.appchat:id/search_badge = 0x7f0800a2
com.hamode.appchat:id/scrollView = 0x7f0800a1
com.hamode.appchat:id/scrollIndicatorUp = 0x7f0800a0
com.hamode.appchat:attr/thumbTextPadding = 0x7f030158
com.hamode.appchat:style/TextAppearance.AppCompat.Medium = 0x7f0f00ef
com.hamode.appchat:id/screen = 0x7f08009e
com.hamode.appchat:id/save_non_transition_alpha = 0x7f08009c
com.hamode.appchat:string/project_id = 0x7f0e0046
com.hamode.appchat:id/rtl = 0x7f08009b
com.hamode.appchat:style/Preference.Category = 0x7f0f00af
com.hamode.appchat:id/right_icon = 0x7f080099
com.hamode.appchat:attr/fontProviderAuthority = 0x7f0300a1
com.hamode.appchat:id/right = 0x7f080098
com.hamode.appchat:id/progress_horizontal = 0x7f080094
com.hamode.appchat:id/progress_circular = 0x7f080093
com.hamode.appchat:styleable/LinearLayoutCompat = 0x7f100022
com.hamode.appchat:id/preferences_sliding_pane_layout = 0x7f080092
com.hamode.appchat:id/title_template = 0x7f0800d4
com.hamode.appchat:id/preferences_detail = 0x7f080090
com.hamode.appchat:id/parent_matrix = 0x7f08008f
com.hamode.appchat:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.hamode.appchat:id/notification_main_column_container = 0x7f08008b
com.hamode.appchat:id/notification_main_column = 0x7f08008a
com.hamode.appchat:id/multiply = 0x7f080085
com.hamode.appchat:id/middle = 0x7f080084
com.hamode.appchat:drawable/abc_switch_thumb_material = 0x7f070045
com.hamode.appchat:style/Platform.V21.AppCompat.Light = 0x7f0f00aa
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0f0013
com.hamode.appchat:layout/notification_template_part_chronometer = 0x7f0b0027
com.hamode.appchat:id/message = 0x7f080083
com.hamode.appchat:id/locale = 0x7f080081
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Title = 0x7f0f0025
com.hamode.appchat:id/list_item = 0x7f080080
com.hamode.appchat:id/listMode = 0x7f08007f
com.hamode.appchat:id/line3 = 0x7f08007e
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0f0108
com.hamode.appchat:style/Preference.SeekBarPreference = 0x7f0f00be
com.hamode.appchat:id/line1 = 0x7f08007d
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Headline = 0x7f0f0015
com.hamode.appchat:attr/finishSecondaryWithPrimary = 0x7f03009d
com.hamode.appchat:id/action_bar_root = 0x7f08002a
com.hamode.appchat:id/left = 0x7f08007b
com.hamode.appchat:id/item_touch_helper_previous_elevation = 0x7f08007a
com.hamode.appchat:id/italic = 0x7f080079
com.hamode.appchat:styleable/BackgroundStyle = 0x7f100011
com.hamode.appchat:id/info = 0x7f080078
com.hamode.appchat:id/image = 0x7f080077
com.hamode.appchat:drawable/common_google_signin_btn_text_dark_normal = 0x7f07006a
com.hamode.appchat:id/on = 0x7f08008d
com.hamode.appchat:id/wide = 0x7f0800e6
com.hamode.appchat:id/ifRoom = 0x7f080076
com.hamode.appchat:id/icon_group = 0x7f080074
com.hamode.appchat:color/bright_foreground_disabled_material_light = 0x7f050021
com.hamode.appchat:id/icon = 0x7f080072
com.hamode.appchat:attr/title = 0x7f030160
com.hamode.appchat:id/homeAsUp = 0x7f080071
com.hamode.appchat:id/hide_ime_id = 0x7f08006f
com.hamode.appchat:id/group_divider = 0x7f08006e
com.hamode.appchat:drawable/abc_ic_menu_overflow_material = 0x7f07001c
com.hamode.appchat:style/PreferenceCategoryTitleTextStyle = 0x7f0f00c4
com.hamode.appchat:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0f003f
com.hamode.appchat:id/fill_vertical = 0x7f080069
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f0028
com.hamode.appchat:string/fallback_menu_item_share_link = 0x7f0e003c
com.hamode.appchat:id/edit_text_id = 0x7f080063
com.hamode.appchat:id/edit_query = 0x7f080062
com.hamode.appchat:id/dialog_button = 0x7f080060
com.hamode.appchat:dimen/abc_text_size_medium_material = 0x7f060046
com.hamode.appchat:id/default_activity_button = 0x7f08005f
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0f0027
com.hamode.appchat:integer/google_play_services_version = 0x7f090004
com.hamode.appchat:id/dark = 0x7f08005d
com.hamode.appchat:string/common_open_on_phone = 0x7f0e0034
com.hamode.appchat:id/customPanel = 0x7f08005c
com.hamode.appchat:id/contentPanel = 0x7f08005a
com.hamode.appchat:attr/spinnerStyle = 0x7f030129
com.hamode.appchat:id/content = 0x7f080059
com.hamode.appchat:style/Animation.AppCompat.Dialog = 0x7f0f0002
com.hamode.appchat:attr/activityAction = 0x7f030021
com.hamode.appchat:id/clip_horizontal = 0x7f080056
com.hamode.appchat:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f07001f
com.hamode.appchat:id/chronometer = 0x7f080055
com.hamode.appchat:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.hamode.appchat:color/androidx_core_ripple_material_light = 0x7f05001a
com.hamode.appchat:id/center_vertical = 0x7f080052
com.hamode.appchat:id/center_horizontal = 0x7f080051
com.hamode.appchat:styleable/Spinner = 0x7f100038
com.hamode.appchat:id/center = 0x7f080050
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0f0030
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Menu = 0x7f0f001d
com.hamode.appchat:layout/abc_action_bar_title_item = 0x7f0b0000
com.hamode.appchat:id/browser_actions_menu_view = 0x7f08004e
com.hamode.appchat:styleable/ActivityRule = 0x7f100007
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0f0019
com.hamode.appchat:dimen/notification_small_icon_size_as_large = 0x7f060071
com.hamode.appchat:id/browser_actions_menu_item_text = 0x7f08004c
com.hamode.appchat:layout/preference_information_material = 0x7f0b0030
com.hamode.appchat:id/browser_actions_header_text = 0x7f08004a
com.hamode.appchat:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001e
com.hamode.appchat:id/blocking = 0x7f080047
com.hamode.appchat:id/accessibility_custom_action_14 = 0x7f08000d
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0f0085
com.hamode.appchat:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0f006e
com.hamode.appchat:attr/textAppearanceLargePopupMenu = 0x7f03014b
com.hamode.appchat:string/fallback_menu_item_copy_link = 0x7f0e003a
com.hamode.appchat:style/Animation.AppCompat.Tooltip = 0x7f0f0004
com.hamode.appchat:id/ltr = 0x7f080082
com.hamode.appchat:id/report_drawn = 0x7f080097
com.hamode.appchat:id/async = 0x7f080044
com.hamode.appchat:id/alwaysDisallow = 0x7f080042
com.hamode.appchat:layout/preference_widget_seekbar = 0x7f0b0035
com.hamode.appchat:id/alertTitle = 0x7f08003e
com.hamode.appchat:attr/contentInsetEnd = 0x7f030065
com.hamode.appchat:id/adjust_width = 0x7f08003d
com.hamode.appchat:attr/popupMenuStyle = 0x7f0300f7
com.hamode.appchat:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0f0174
com.hamode.appchat:color/common_google_signin_btn_text_light = 0x7f050033
com.hamode.appchat:layout/browser_actions_context_menu_page = 0x7f0b001c
com.hamode.appchat:id/adjust_height = 0x7f08003c
com.hamode.appchat:styleable/SwitchPreference = 0x7f10003f
com.hamode.appchat:string/android.credentials.TYPE_PASSWORD_CREDENTIAL = 0x7f0e001b
com.hamode.appchat:id/add = 0x7f08003a
com.hamode.appchat:style/Base.TextAppearance.AppCompat = 0x7f0f000c
com.hamode.appchat:id/activity_chooser_view_content = 0x7f080039
com.hamode.appchat:attr/tintMode = 0x7f03015f
com.hamode.appchat:id/actions = 0x7f080038
com.hamode.appchat:style/TextAppearance.AppCompat.Body2 = 0x7f0f00e0
com.hamode.appchat:layout/select_dialog_singlechoice_material = 0x7f0b003b
com.hamode.appchat:attr/showDividers = 0x7f030120
com.hamode.appchat:id/right_side = 0x7f08009a
com.hamode.appchat:id/action_mode_close_button = 0x7f080036
com.hamode.appchat:id/action_mode_bar_stub = 0x7f080035
com.hamode.appchat:dimen/notification_right_side_padding_top = 0x7f06006f
com.hamode.appchat:style/Widget.AppCompat.SearchView = 0x7f0f0173
com.hamode.appchat:attr/actionModeSelectAllDrawable = 0x7f030018
com.hamode.appchat:string/v7_preference_off = 0x7f0e004a
com.hamode.appchat:id/action_mode_bar = 0x7f080034
com.hamode.appchat:id/action_menu_presenter = 0x7f080033
com.hamode.appchat:id/action_image = 0x7f080031
com.hamode.appchat:id/action_bar_title = 0x7f08002d
com.hamode.appchat:id/action_bar_subtitle = 0x7f08002c
com.hamode.appchat:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.hamode.appchat:style/TextAppearance.AppCompat.Small = 0x7f0f00f4
com.hamode.appchat:id/action_bar_container = 0x7f080029
com.hamode.appchat:id/action_bar = 0x7f080027
com.hamode.appchat:id/accessibility_custom_action_5 = 0x7f080022
com.hamode.appchat:styleable/AnimatedStateListDrawableTransition = 0x7f10000b
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0f00d6
com.hamode.appchat:id/accessibility_custom_action_4 = 0x7f080021
com.hamode.appchat:layout/abc_action_bar_up_container = 0x7f0b0001
com.hamode.appchat:id/accessibility_custom_action_30 = 0x7f08001f
com.hamode.appchat:style/Theme.AppCompat = 0x7f0f0116
com.hamode.appchat:id/accessibility_custom_action_28 = 0x7f08001c
com.hamode.appchat:style/LaunchTheme = 0x7f0f00a2
com.hamode.appchat:id/accessibility_custom_action_27 = 0x7f08001b
com.hamode.appchat:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0f0054
com.hamode.appchat:animator/fragment_close_enter = 0x7f020000
com.hamode.appchat:id/accessibility_custom_action_25 = 0x7f080019
com.hamode.appchat:drawable/googleg_disabled_color_18 = 0x7f070071
com.hamode.appchat:id/accessibility_custom_action_24 = 0x7f080018
com.hamode.appchat:id/accessibility_custom_action_23 = 0x7f080017
com.hamode.appchat:anim/abc_slide_in_top = 0x7f010007
com.hamode.appchat:id/accessibility_custom_action_21 = 0x7f080015
com.hamode.appchat:id/accessibility_custom_action_2 = 0x7f080013
com.hamode.appchat:id/accessibility_custom_action_19 = 0x7f080012
com.hamode.appchat:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0f007f
com.hamode.appchat:id/accessibility_custom_action_17 = 0x7f080010
com.hamode.appchat:style/Theme.AppCompat.NoActionBar = 0x7f0f012a
com.hamode.appchat:id/accessibility_custom_action_15 = 0x7f08000e
com.hamode.appchat:attr/elevation = 0x7f030090
com.hamode.appchat:id/accessibility_custom_action_12 = 0x7f08000b
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0f0156
com.hamode.appchat:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070006
com.hamode.appchat:id/save_overlay_view = 0x7f08009d
com.hamode.appchat:id/accessibility_action_clickable_span = 0x7f080006
com.hamode.appchat:id/SHIFT = 0x7f080004
com.hamode.appchat:id/META = 0x7f080003
com.hamode.appchat:id/FUNCTION = 0x7f080002
com.hamode.appchat:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300ce
com.hamode.appchat:id/CTRL = 0x7f080001
com.hamode.appchat:attr/paddingEnd = 0x7f0300ef
com.hamode.appchat:drawable/tooltip_frame_light = 0x7f07008e
com.hamode.appchat:drawable/preference_list_divider_material = 0x7f07008c
com.hamode.appchat:drawable/common_google_signin_btn_icon_light_focused = 0x7f070065
com.hamode.appchat:drawable/notify_panel_notification_icon_bg = 0x7f07008b
com.hamode.appchat:drawable/common_google_signin_btn_text_light_normal = 0x7f07006f
com.hamode.appchat:dimen/abc_action_bar_elevation_material = 0x7f060005
com.hamode.appchat:style/Base.V23.Theme.AppCompat.Light = 0x7f0f0059
com.hamode.appchat:drawable/notification_template_icon_bg = 0x7f070088
com.hamode.appchat:drawable/notification_bg_normal = 0x7f070084
com.hamode.appchat:attr/entryValues = 0x7f030094
com.hamode.appchat:id/expanded_menu = 0x7f080066
com.hamode.appchat:drawable/notification_bg_low_normal = 0x7f070082
com.hamode.appchat:drawable/notification_bg_low = 0x7f070081
com.hamode.appchat:dimen/abc_text_size_display_3_material = 0x7f060042
com.hamode.appchat:attr/initialActivityCount = 0x7f0300bb
com.hamode.appchat:drawable/ic_passkey = 0x7f07007c
com.hamode.appchat:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0f016c
com.hamode.appchat:drawable/common_google_signin_btn_icon_dark = 0x7f07005f
com.hamode.appchat:drawable/notification_template_icon_low_bg = 0x7f070089
com.hamode.appchat:attr/spanCount = 0x7f030126
com.hamode.appchat:styleable/MenuView = 0x7f100029
com.hamode.appchat:drawable/ic_call_decline_low = 0x7f070079
com.hamode.appchat:drawable/ic_call_decline = 0x7f070078
com.hamode.appchat:string/abc_searchview_description_search = 0x7f0e0015
com.hamode.appchat:id/action_menu_divider = 0x7f080032
com.hamode.appchat:attr/lStar = 0x7f0300c2
com.hamode.appchat:attr/secondaryActivityName = 0x7f030116
com.hamode.appchat:drawable/ic_call_answer = 0x7f070074
com.hamode.appchat:id/tag_on_receive_content_listener = 0x7f0800c5
com.hamode.appchat:dimen/notification_subtext_size = 0x7f060072
com.hamode.appchat:attr/panelMenuListTheme = 0x7f0300f3
com.hamode.appchat:color/call_notification_decline_color = 0x7f05002d
com.hamode.appchat:dimen/highlight_alpha_material_dark = 0x7f06005d
com.hamode.appchat:color/ic_launcher_background = 0x7f050043
com.hamode.appchat:drawable/common_google_signin_btn_text_light = 0x7f07006d
com.hamode.appchat:drawable/common_google_signin_btn_text_disabled = 0x7f07006c
com.hamode.appchat:dimen/notification_main_column_padding_top = 0x7f06006c
com.hamode.appchat:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f07006b
com.hamode.appchat:attr/queryHint = 0x7f03010a
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Caption = 0x7f0f0010
com.hamode.appchat:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004b
com.hamode.appchat:attr/buttonPanelSideLayout = 0x7f030048
com.hamode.appchat:id/tag_accessibility_clickable_spans = 0x7f0800c1
com.hamode.appchat:drawable/common_google_signin_btn_text_dark_focused = 0x7f070069
com.hamode.appchat:drawable/common_google_signin_btn_icon_disabled = 0x7f070063
com.hamode.appchat:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0048
com.hamode.appchat:dimen/tooltip_horizontal_padding = 0x7f06007d
com.hamode.appchat:drawable/common_google_signin_btn_icon_dark_focused = 0x7f070060
com.hamode.appchat:id/titleDividerNoCustom = 0x7f0800d3
com.hamode.appchat:drawable/btn_radio_on_mtrl = 0x7f07005c
com.hamode.appchat:dimen/notification_top_pad_large_text = 0x7f060074
com.hamode.appchat:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.hamode.appchat:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07005b
com.hamode.appchat:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070059
com.hamode.appchat:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0f008d
com.hamode.appchat:drawable/abc_list_pressed_holo_light = 0x7f07002f
com.hamode.appchat:drawable/btn_checkbox_unchecked_mtrl = 0x7f070058
com.hamode.appchat:color/common_google_signin_btn_text_dark_pressed = 0x7f050032
com.hamode.appchat:drawable/abc_vector_test = 0x7f070055
com.hamode.appchat:drawable/abc_list_longpressed_holo = 0x7f07002d
com.hamode.appchat:drawable/abc_text_select_handle_right_mtrl_dark = 0x7f07004e
com.hamode.appchat:style/Base.Animation.AppCompat.Tooltip = 0x7f0f0009
com.hamode.appchat:drawable/abc_text_select_handle_left_mtrl_light = 0x7f07004b
com.hamode.appchat:attr/actionBarSize = 0x7f030003
com.hamode.appchat:drawable/abc_text_cursor_material = 0x7f070049
com.hamode.appchat:attr/splitRatio = 0x7f030130
com.hamode.appchat:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070048
com.hamode.appchat:style/Theme.PlayCore.Transparent = 0x7f0f012c
com.hamode.appchat:attr/listPreferredItemHeight = 0x7f0300d5
com.hamode.appchat:color/browser_actions_bg_grey = 0x7f050026
com.hamode.appchat:drawable/abc_tab_indicator_material = 0x7f070047
com.hamode.appchat:string/google_crash_reporting_api_key = 0x7f0e0042
com.hamode.appchat:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070000
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0157
com.hamode.appchat:id/accessibility_custom_action_9 = 0x7f080026
com.hamode.appchat:attr/colorAccent = 0x7f030058
com.hamode.appchat:drawable/abc_spinner_textfield_background_material = 0x7f070044
com.hamode.appchat:attr/drawableTint = 0x7f030085
com.hamode.appchat:drawable/abc_spinner_mtrl_am_alpha = 0x7f070043
com.hamode.appchat:drawable/abc_seekbar_thumb_material = 0x7f070040
com.hamode.appchat:drawable/abc_scrubber_track_mtrl_alpha = 0x7f07003f
com.hamode.appchat:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f07003e
com.hamode.appchat:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07003d
com.hamode.appchat:style/ThemeOverlay.AppCompat.Dialog = 0x7f0f0133
com.hamode.appchat:color/bright_foreground_inverse_material_dark = 0x7f050022
com.hamode.appchat:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07003b
com.hamode.appchat:drawable/abc_ratingbar_small_material = 0x7f07003a
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0f0034
com.hamode.appchat:drawable/abc_popup_background_mtrl_mult = 0x7f070037
com.hamode.appchat:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0f014b
com.hamode.appchat:style/Widget.AppCompat.Light.PopupMenu = 0x7f0f0162
com.hamode.appchat:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070036
com.hamode.appchat:attr/dropdownPreferenceStyle = 0x7f03008b
com.hamode.appchat:drawable/abc_list_selector_disabled_holo_light = 0x7f070033
com.hamode.appchat:dimen/abc_control_padding_material = 0x7f06001a
com.hamode.appchat:drawable/abc_list_pressed_holo_dark = 0x7f07002e
com.hamode.appchat:drawable/abc_ic_star_half_black_36dp = 0x7f070025
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0f00d8
com.hamode.appchat:drawable/abc_ratingbar_indicator_material = 0x7f070038
com.hamode.appchat:style/Preference.DialogPreference.EditTextPreference.Material = 0x7f0f00b5
com.hamode.appchat:attr/fontProviderFetchTimeout = 0x7f0300a4
com.hamode.appchat:attr/allowDividerBelow = 0x7f03002b
com.hamode.appchat:id/accessibility_custom_action_22 = 0x7f080016
com.hamode.appchat:attr/switchPreferenceCompatStyle = 0x7f030143
com.hamode.appchat:drawable/abc_ic_star_half_black_16dp = 0x7f070024
com.hamode.appchat:drawable/abc_item_background_holo_light = 0x7f070029
com.hamode.appchat:drawable/abc_ic_star_black_48dp = 0x7f070023
com.hamode.appchat:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0f009a
com.hamode.appchat:drawable/abc_ic_star_black_36dp = 0x7f070022
com.hamode.appchat:layout/image_frame = 0x7f0b0020
com.hamode.appchat:id/search_close_btn = 0x7f0800a5
com.hamode.appchat:animator/fragment_open_enter = 0x7f020004
com.hamode.appchat:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001b
com.hamode.appchat:attr/showAsAction = 0x7f03011f
com.hamode.appchat:attr/showTitle = 0x7f030123
com.hamode.appchat:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001a
com.hamode.appchat:animator/fragment_fade_enter = 0x7f020002
com.hamode.appchat:string/common_signin_button_text = 0x7f0e0035
com.hamode.appchat:attr/dialogPreferenceStyle = 0x7f030075
com.hamode.appchat:dimen/abc_dialog_min_width_major = 0x7f060022
com.hamode.appchat:drawable/abc_ic_go_search_api_material = 0x7f070019
com.hamode.appchat:attr/listPreferredItemPaddingLeft = 0x7f0300d9
com.hamode.appchat:color/secondary_text_disabled_material_light = 0x7f050060
com.hamode.appchat:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070018
com.hamode.appchat:id/browser_actions_menu_items = 0x7f08004d
com.hamode.appchat:drawable/abc_ic_clear_material = 0x7f070017
com.hamode.appchat:attr/buttonGravity = 0x7f030046
com.hamode.appchat:style/Preference.Material = 0x7f0f00bb
com.hamode.appchat:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0f007d
com.hamode.appchat:styleable/SplitPairFilter = 0x7f100039
com.hamode.appchat:style/Base.AlertDialog.AppCompat = 0x7f0f0005
com.hamode.appchat:drawable/abc_dialog_material_background = 0x7f070013
com.hamode.appchat:attr/navigationMode = 0x7f0300e7
com.hamode.appchat:drawable/abc_control_background_material = 0x7f070012
com.hamode.appchat:attr/isLightTheme = 0x7f0300bd
com.hamode.appchat:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000e
com.hamode.appchat:drawable/ic_launcher_foreground = 0x7f07007a
com.hamode.appchat:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000c
com.hamode.appchat:styleable/ViewBackgroundHelper = 0x7f100044
com.hamode.appchat:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001d
com.hamode.appchat:color/abc_hint_foreground_material_dark = 0x7f050005
com.hamode.appchat:attr/preferenceStyle = 0x7f030103
com.hamode.appchat:drawable/abc_btn_radio_material_anim = 0x7f07000a
com.hamode.appchat:style/Widget.AppCompat.Light.SearchView = 0x7f0f0164
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0f00d1
com.hamode.appchat:drawable/abc_btn_default_mtrl_shape = 0x7f070008
com.hamode.appchat:attr/allowDividerAbove = 0x7f030029
com.hamode.appchat:drawable/abc_btn_colored_material = 0x7f070007
com.hamode.appchat:layout/preference_widget_switch_compat = 0x7f0b0038
com.hamode.appchat:drawable/abc_btn_check_material = 0x7f070003
com.hamode.appchat:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070005
com.hamode.appchat:id/home = 0x7f080070
com.hamode.appchat:styleable/SignInButton = 0x7f100037
com.hamode.appchat:drawable/abc_btn_check_material_anim = 0x7f070004
com.hamode.appchat:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f0130
com.hamode.appchat:dimen/tooltip_precise_anchor_threshold = 0x7f060080
com.hamode.appchat:style/Widget.AppCompat.Light.ActionButton = 0x7f0f0159
com.hamode.appchat:dimen/tooltip_precise_anchor_extra_offset = 0x7f06007f
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0f00d3
com.hamode.appchat:string/abc_capital_on = 0x7f0e0007
com.hamode.appchat:dimen/tooltip_margin = 0x7f06007e
com.hamode.appchat:attr/dropdownListPreferredItemHeight = 0x7f03008a
com.hamode.appchat:style/Base.DialogWindowTitle.AppCompat = 0x7f0f000a
com.hamode.appchat:dimen/preference_seekbar_value_minWidth = 0x7f060079
com.hamode.appchat:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0f0088
com.hamode.appchat:dimen/preference_seekbar_padding_vertical = 0x7f060078
com.hamode.appchat:color/material_grey_300 = 0x7f05004a
com.hamode.appchat:dimen/preference_seekbar_padding_horizontal = 0x7f060077
com.hamode.appchat:attr/srcCompat = 0x7f030132
com.hamode.appchat:dimen/preference_icon_minWidth = 0x7f060076
com.hamode.appchat:layout/support_simple_spinner_dropdown_item = 0x7f0b003c
com.hamode.appchat:dimen/notification_top_pad = 0x7f060073
com.hamode.appchat:drawable/notification_bg_normal_pressed = 0x7f070085
com.hamode.appchat:dimen/notification_small_icon_background_padding = 0x7f060070
com.hamode.appchat:drawable/abc_action_bar_item_background_material = 0x7f070001
com.hamode.appchat:dimen/notification_right_icon_size = 0x7f06006e
com.hamode.appchat:dimen/notification_large_icon_width = 0x7f06006b
com.hamode.appchat:string/google_api_key = 0x7f0e0040
com.hamode.appchat:dimen/notification_large_icon_height = 0x7f06006a
com.hamode.appchat:attr/colorBackgroundFloating = 0x7f030059
com.hamode.appchat:string/abc_menu_delete_shortcut_label = 0x7f0e000a
com.hamode.appchat:dimen/notification_content_margin_start = 0x7f060069
com.hamode.appchat:dimen/notification_big_circle_margin = 0x7f060068
com.hamode.appchat:style/AlertDialog.AppCompat = 0x7f0f0000
com.hamode.appchat:dimen/notification_action_text_size = 0x7f060067
com.hamode.appchat:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.hamode.appchat:attr/fontFamily = 0x7f0300a0
com.hamode.appchat:color/foreground_material_light = 0x7f050040
com.hamode.appchat:dimen/notification_action_icon_size = 0x7f060066
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0f00d4
com.hamode.appchat:attr/searchIcon = 0x7f030113
com.hamode.appchat:dimen/hint_pressed_alpha_material_light = 0x7f060062
com.hamode.appchat:color/highlighted_text_material_dark = 0x7f050041
com.hamode.appchat:dimen/hint_pressed_alpha_material_dark = 0x7f060061
com.hamode.appchat:dimen/hint_alpha_material_light = 0x7f060060
com.hamode.appchat:style/Preference.SeekBarPreference.Material = 0x7f0f00bf
com.hamode.appchat:attr/buttonIconDimen = 0x7f030047
com.hamode.appchat:dimen/highlight_alpha_material_light = 0x7f06005e
com.hamode.appchat:dimen/highlight_alpha_material_colored = 0x7f06005c
com.hamode.appchat:dimen/fastscroll_minimum_range = 0x7f06005b
com.hamode.appchat:style/Widget.AppCompat.ProgressBar = 0x7f0f016e
com.hamode.appchat:dimen/fastscroll_margin = 0x7f06005a
com.hamode.appchat:id/action_bar_activity_content = 0x7f080028
com.hamode.appchat:dimen/disabled_alpha_material_dark = 0x7f060057
com.hamode.appchat:layout/notification_template_part_time = 0x7f0b0028
com.hamode.appchat:dimen/compat_notification_large_icon_max_width = 0x7f060056
com.hamode.appchat:dimen/tooltip_vertical_padding = 0x7f060081
com.hamode.appchat:string/abc_menu_meta_shortcut_label = 0x7f0e000d
com.hamode.appchat:attr/windowMinWidthMajor = 0x7f03017f
com.hamode.appchat:style/AlertDialog.AppCompat.Light = 0x7f0f0001
com.hamode.appchat:dimen/compat_button_inset_vertical_material = 0x7f060051
com.hamode.appchat:dimen/compat_button_inset_horizontal_material = 0x7f060050
com.hamode.appchat:styleable/ButtonBarLayout = 0x7f100012
com.hamode.appchat:dimen/abc_text_size_small_material = 0x7f060049
com.hamode.appchat:styleable/Toolbar = 0x7f100042
com.hamode.appchat:dimen/abc_text_size_menu_header_material = 0x7f060047
com.hamode.appchat:attr/switchTextOff = 0x7f030147
com.hamode.appchat:color/common_google_signin_btn_text_light_default = 0x7f050034
com.hamode.appchat:dimen/abc_text_size_headline_material = 0x7f060044
com.hamode.appchat:id/select_dialog_listview = 0x7f0800ae
com.hamode.appchat:attr/isPreferenceVisible = 0x7f0300be
com.hamode.appchat:style/Widget.AppCompat.Button.Borderless = 0x7f0f0142
com.hamode.appchat:attr/activityName = 0x7f030023
com.hamode.appchat:dimen/abc_text_size_display_2_material = 0x7f060041
com.hamode.appchat:id/tag_accessibility_pane_title = 0x7f0800c3
com.hamode.appchat:drawable/common_full_open_on_phone = 0x7f07005e
com.hamode.appchat:style/Base.Theme.AppCompat.Dialog = 0x7f0f003e
com.hamode.appchat:string/call_notification_answer_video_action = 0x7f0e001f
com.hamode.appchat:id/end = 0x7f080064
com.hamode.appchat:styleable/FragmentContainerView = 0x7f10001f
com.hamode.appchat:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.hamode.appchat:color/common_google_signin_btn_text_light_disabled = 0x7f050035
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0f00ff
com.hamode.appchat:dimen/abc_search_view_preferred_height = 0x7f060036
com.hamode.appchat:layout/abc_list_menu_item_layout = 0x7f0b0010
com.hamode.appchat:dimen/abc_list_item_height_large_material = 0x7f060030
com.hamode.appchat:dimen/abc_dialog_padding_top_material = 0x7f060025
com.hamode.appchat:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0074
com.hamode.appchat:color/switch_thumb_normal_material_light = 0x7f050066
com.hamode.appchat:dimen/abc_floating_window_z = 0x7f06002f
com.hamode.appchat:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.hamode.appchat:drawable/notification_tile_bg = 0x7f07008a
com.hamode.appchat:attr/actionMenuTextAppearance = 0x7f03000e
com.hamode.appchat:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.hamode.appchat:style/TextAppearance.AppCompat.Caption = 0x7f0f00e2
com.hamode.appchat:string/not_set = 0x7f0e0044
com.hamode.appchat:attr/windowNoTitle = 0x7f030181
com.hamode.appchat:dimen/abc_control_corner_material = 0x7f060018
com.hamode.appchat:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.hamode.appchat:string/copy_toast_msg = 0x7f0e0038
com.hamode.appchat:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07003c
com.hamode.appchat:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.hamode.appchat:attr/subtitleTextAppearance = 0x7f03013a
com.hamode.appchat:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0f011b
com.hamode.appchat:id/text = 0x7f0800cd
com.hamode.appchat:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000b
com.hamode.appchat:dimen/abc_dialog_padding_material = 0x7f060024
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.Button = 0x7f0f0104
com.hamode.appchat:attr/fontProviderQuery = 0x7f0300a6
com.hamode.appchat:attr/progressBarPadding = 0x7f030107
com.hamode.appchat:dimen/abc_dialog_min_width_minor = 0x7f060023
com.hamode.appchat:style/Platform.Widget.AppCompat.Spinner = 0x7f0f00ad
com.hamode.appchat:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.hamode.appchat:color/material_grey_850 = 0x7f05004e
com.hamode.appchat:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.hamode.appchat:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.hamode.appchat:style/Base.Animation.AppCompat.DropDownUp = 0x7f0f0008
com.hamode.appchat:drawable/abc_btn_borderless_material = 0x7f070002
com.hamode.appchat:attr/widgetLayout = 0x7f030177
com.hamode.appchat:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.hamode.appchat:color/abc_hint_foreground_material_light = 0x7f050006
com.hamode.appchat:attr/defaultQueryHint = 0x7f03006e
com.hamode.appchat:dimen/abc_config_prefDialogWidth = 0x7f060017
com.hamode.appchat:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.hamode.appchat:id/view_tree_lifecycle_owner = 0x7f0800e1
com.hamode.appchat:dimen/abc_button_padding_vertical_material = 0x7f060015
com.hamode.appchat:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.hamode.appchat:style/NormalTheme = 0x7f0f00a3
com.hamode.appchat:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.hamode.appchat:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.hamode.appchat:id/shortcut = 0x7f0800af
com.hamode.appchat:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.hamode.appchat:dimen/abc_action_button_min_width_material = 0x7f06000e
com.hamode.appchat:attr/drawableTintMode = 0x7f030086
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0f0084
com.hamode.appchat:string/status_bar_notification_info_overflow = 0x7f0e0048
com.hamode.appchat:dimen/abc_control_inset_material = 0x7f060019
com.hamode.appchat:attr/selectableItemBackgroundBorderless = 0x7f03011c
com.hamode.appchat:dimen/abc_action_bar_default_height_material = 0x7f060002
com.hamode.appchat:attr/selectable = 0x7f03011a
com.hamode.appchat:drawable/abc_ratingbar_material = 0x7f070039
com.hamode.appchat:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.hamode.appchat:color/tooltip_background_light = 0x7f050068
com.hamode.appchat:id/beginning = 0x7f080046
com.hamode.appchat:color/tooltip_background_dark = 0x7f050067
com.hamode.appchat:color/preference_fallback_accent_color = 0x7f050052
com.hamode.appchat:drawable/abc_list_focused_holo = 0x7f07002c
com.hamode.appchat:attr/alertDialogTheme = 0x7f030028
com.hamode.appchat:styleable/AnimatedStateListDrawableItem = 0x7f10000a
com.hamode.appchat:color/switch_thumb_material_light = 0x7f050064
com.hamode.appchat:attr/actionBarTheme = 0x7f030009
com.hamode.appchat:color/switch_thumb_disabled_material_light = 0x7f050062
com.hamode.appchat:attr/preferenceScreenStyle = 0x7f030102
com.hamode.appchat:layout/preference_widget_seekbar_material = 0x7f0b0036
com.hamode.appchat:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070030
com.hamode.appchat:color/switch_thumb_disabled_material_dark = 0x7f050061
com.hamode.appchat:color/secondary_text_default_material_light = 0x7f05005e
com.hamode.appchat:color/secondary_text_default_material_dark = 0x7f05005d
com.hamode.appchat:string/abc_menu_alt_shortcut_label = 0x7f0e0008
com.hamode.appchat:attr/layout_behavior = 0x7f0300c8
com.hamode.appchat:color/primary_text_disabled_material_light = 0x7f05005a
com.hamode.appchat:layout/abc_screen_content_include = 0x7f0b0014
com.hamode.appchat:color/primary_text_disabled_material_dark = 0x7f050059
com.hamode.appchat:id/clip_vertical = 0x7f080057
com.hamode.appchat:styleable/PreferenceFragment = 0x7f10002e
com.hamode.appchat:color/primary_text_default_material_light = 0x7f050058
com.hamode.appchat:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.hamode.appchat:drawable/abc_item_background_holo_dark = 0x7f070028
com.hamode.appchat:color/primary_text_default_material_dark = 0x7f050057
com.hamode.appchat:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0f00d5
com.hamode.appchat:dimen/abc_text_size_body_1_material = 0x7f06003c
com.hamode.appchat:drawable/abc_list_divider_material = 0x7f07002a
com.hamode.appchat:color/primary_material_dark = 0x7f050055
com.hamode.appchat:dimen/abc_list_item_height_material = 0x7f060031
com.hamode.appchat:string/abc_search_hint = 0x7f0e0012
com.hamode.appchat:attr/ratingBarStyle = 0x7f03010d
com.hamode.appchat:color/primary_dark_material_light = 0x7f050054
com.hamode.appchat:style/TextAppearance.AppCompat.Display4 = 0x7f0f00e6
com.hamode.appchat:color/notification_icon_bg_color = 0x7f050051
com.hamode.appchat:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0f0068
com.hamode.appchat:color/browser_actions_divider_color = 0x7f050027
com.hamode.appchat:drawable/common_google_signin_btn_text_light_normal_background = 0x7f070070
com.hamode.appchat:string/common_google_play_services_install_button = 0x7f0e0028
com.hamode.appchat:color/material_grey_50 = 0x7f05004b
com.hamode.appchat:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0f014d
com.hamode.appchat:attr/iconSpaceReserved = 0x7f0300b3
com.hamode.appchat:id/search_go_btn = 0x7f0800a7
com.hamode.appchat:drawable/ic_arrow_down_24dp = 0x7f070073
com.hamode.appchat:color/material_blue_grey_900 = 0x7f050045
com.hamode.appchat:attr/controlBackground = 0x7f03006b
com.hamode.appchat:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070016
com.hamode.appchat:style/TextAppearance.Compat.Notification = 0x7f0f010e
com.hamode.appchat:styleable/ListPopupWindow = 0x7f100024
com.hamode.appchat:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.hamode.appchat:attr/tickMarkTint = 0x7f03015c
com.hamode.appchat:attr/font = 0x7f03009f
com.hamode.appchat:layout/abc_list_menu_item_radio = 0x7f0b0011
com.hamode.appchat:color/abc_primary_text_disable_only_material_light = 0x7f050009
com.hamode.appchat:color/error_color_material_light = 0x7f05003e
com.hamode.appchat:color/error_color_material_dark = 0x7f05003d
com.hamode.appchat:attr/buttonBarNegativeButtonStyle = 0x7f030041
com.hamode.appchat:color/dim_foreground_material_light = 0x7f05003c
com.hamode.appchat:color/dim_foreground_material_dark = 0x7f05003b
com.hamode.appchat:style/Base.V7.Theme.AppCompat = 0x7f0f005f
com.hamode.appchat:color/dim_foreground_disabled_material_light = 0x7f05003a
com.hamode.appchat:drawable/abc_text_select_handle_middle_mtrl_light = 0x7f07004d
com.hamode.appchat:color/dim_foreground_disabled_material_dark = 0x7f050039
com.hamode.appchat:style/Widget.AppCompat.PopupWindow = 0x7f0f016d
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0f0018
com.hamode.appchat:color/common_google_signin_btn_text_dark_focused = 0x7f050031
com.hamode.appchat:attr/trackTint = 0x7f030170
com.hamode.appchat:attr/titleMarginStart = 0x7f030164
com.hamode.appchat:color/common_google_signin_btn_text_dark_disabled = 0x7f050030
com.hamode.appchat:id/accessibility_custom_action_18 = 0x7f080011
com.hamode.appchat:string/abc_searchview_description_voice = 0x7f0e0017
com.hamode.appchat:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.hamode.appchat:color/switch_thumb_material_dark = 0x7f050063
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Small = 0x7f0f0021
com.hamode.appchat:id/tag_on_apply_window_listener = 0x7f0800c4
com.hamode.appchat:color/common_google_signin_btn_text_dark = 0x7f05002e
com.hamode.appchat:drawable/abc_text_select_handle_left_mtrl_dark = 0x7f07004a
com.hamode.appchat:color/abc_tint_default = 0x7f050013
com.hamode.appchat:style/Widget.Support.CoordinatorLayout = 0x7f0f0181
com.hamode.appchat:color/button_material_light = 0x7f05002b
com.hamode.appchat:style/TextAppearance.AppCompat.Subhead = 0x7f0f00f6
com.hamode.appchat:color/bright_foreground_material_light = 0x7f050025
com.hamode.appchat:id/notification_background = 0x7f080089
com.hamode.appchat:color/accent_material_light = 0x7f050019
com.hamode.appchat:drawable/notification_bg_low_pressed = 0x7f070083
com.hamode.appchat:attr/popupWindowStyle = 0x7f0300f9
com.hamode.appchat:color/bright_foreground_inverse_material_light = 0x7f050023
com.hamode.appchat:color/background_material_light = 0x7f05001f
com.hamode.appchat:attr/backgroundTint = 0x7f03003c
com.hamode.appchat:color/background_material_dark = 0x7f05001e
com.hamode.appchat:color/background_floating_material_light = 0x7f05001d
com.hamode.appchat:color/background_floating_material_dark = 0x7f05001c
com.hamode.appchat:color/accent_material_dark = 0x7f050018
com.hamode.appchat:attr/trackTintMode = 0x7f030171
com.hamode.appchat:attr/multiChoiceItemLayout = 0x7f0300e4
com.hamode.appchat:color/abc_tint_switch_track = 0x7f050017
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0f00fb
com.hamode.appchat:color/abc_tint_seek_thumb = 0x7f050015
com.hamode.appchat:color/secondary_text_disabled_material_dark = 0x7f05005f
com.hamode.appchat:drawable/abc_list_divider_mtrl_alpha = 0x7f07002b
com.hamode.appchat:color/common_google_signin_btn_tint = 0x7f050038
com.hamode.appchat:dimen/preference_dropdown_padding_start = 0x7f060075
com.hamode.appchat:color/abc_tint_btn_checkable = 0x7f050012
com.hamode.appchat:color/abc_secondary_text_material_light = 0x7f050011
com.hamode.appchat:drawable/launch_background = 0x7f07007e
com.hamode.appchat:attr/customNavigationLayout = 0x7f03006d
com.hamode.appchat:attr/finishPrimaryWithPlaceholder = 0x7f03009b
com.hamode.appchat:color/abc_secondary_text_material_dark = 0x7f050010
com.hamode.appchat:color/abc_search_url_text_selected = 0x7f05000f
com.hamode.appchat:attr/titleMargin = 0x7f030161
com.hamode.appchat:dimen/tooltip_y_offset_touch = 0x7f060083
com.hamode.appchat:attr/adjustable = 0x7f030024
com.hamode.appchat:id/accessibility_custom_action_10 = 0x7f080009
com.hamode.appchat:attr/layout_keyline = 0x7f0300cb
com.hamode.appchat:color/abc_primary_text_material_dark = 0x7f05000a
com.hamode.appchat:color/abc_primary_text_disable_only_material_dark = 0x7f050008
com.hamode.appchat:id/action_context_bar = 0x7f08002f
com.hamode.appchat:color/abc_input_method_navigation_guard = 0x7f050007
com.hamode.appchat:color/switch_thumb_normal_material_dark = 0x7f050065
com.hamode.appchat:id/radio = 0x7f080095
com.hamode.appchat:color/abc_btn_colored_text_material = 0x7f050003
com.hamode.appchat:color/bright_foreground_disabled_material_dark = 0x7f050020
com.hamode.appchat:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.hamode.appchat:id/decor_content_parent = 0x7f08005e
com.hamode.appchat:color/browser_actions_title_color = 0x7f050029
com.hamode.appchat:attr/initialExpandedChildrenCount = 0x7f0300bc
com.hamode.appchat:attr/height = 0x7f0300ae
com.hamode.appchat:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070052
com.hamode.appchat:id/accessibility_custom_action_13 = 0x7f08000c
com.hamode.appchat:attr/actionModeWebSearchDrawable = 0x7f03001c
com.hamode.appchat:color/ripple_material_light = 0x7f05005c
com.hamode.appchat:attr/thumbTint = 0x7f030159
com.hamode.appchat:bool/abc_config_actionMenuItemAllCaps = 0x7f040002
com.hamode.appchat:bool/abc_action_bar_embed_tabs = 0x7f040000
com.hamode.appchat:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0f0063
com.hamode.appchat:anim/abc_popup_exit = 0x7f010004
com.hamode.appchat:attr/windowFixedWidthMinor = 0x7f03017e
com.hamode.appchat:attr/windowFixedWidthMajor = 0x7f03017d
com.hamode.appchat:attr/windowFixedHeightMajor = 0x7f03017b
com.hamode.appchat:attr/windowActionBarOverlay = 0x7f030179
com.hamode.appchat:id/all = 0x7f08003f
com.hamode.appchat:attr/actionBarStyle = 0x7f030005
com.hamode.appchat:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.hamode.appchat:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.hamode.appchat:attr/windowActionBar = 0x7f030178
com.hamode.appchat:id/accessibility_custom_action_8 = 0x7f080025
com.hamode.appchat:attr/drawableSize = 0x7f030083
com.hamode.appchat:attr/voiceIcon = 0x7f030176
com.hamode.appchat:id/none = 0x7f080087
com.hamode.appchat:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f060063
com.hamode.appchat:attr/updatesContinuously = 0x7f030173
com.hamode.appchat:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.hamode.appchat:styleable/AppCompatTextView = 0x7f10000f
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f0038
com.hamode.appchat:drawable/ic_password = 0x7f07007d
com.hamode.appchat:attr/enabled = 0x7f030092
com.hamode.appchat:style/Theme.AppCompat.CompactMenu = 0x7f0f0117
com.hamode.appchat:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0f0113
com.hamode.appchat:id/ghost_view_holder = 0x7f08006d
com.hamode.appchat:dimen/preferences_header_width = 0x7f06007b
com.hamode.appchat:attr/toolbarStyle = 0x7f03016b
com.hamode.appchat:attr/toolbarNavigationButtonStyle = 0x7f03016a
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0f0106
com.hamode.appchat:layout/abc_activity_chooser_view = 0x7f0b0006
com.hamode.appchat:drawable/ic_call_answer_low = 0x7f070075
com.hamode.appchat:attr/titleMarginTop = 0x7f030165
com.hamode.appchat:attr/buttonSize = 0x7f030049
com.hamode.appchat:attr/viewInflaterClass = 0x7f030175
com.hamode.appchat:id/tag_accessibility_heading = 0x7f0800c2
com.hamode.appchat:dimen/tooltip_corner_radius = 0x7f06007c
com.hamode.appchat:style/Base.Widget.AppCompat.ListMenuView = 0x7f0f008a
com.hamode.appchat:attr/fontVariationSettings = 0x7f0300a9
com.hamode.appchat:id/accessibility_custom_action_0 = 0x7f080007
com.hamode.appchat:attr/thumbTintMode = 0x7f03015a
com.hamode.appchat:dimen/abc_dialog_title_divider_material = 0x7f060026
com.hamode.appchat:attr/actionOverflowButtonStyle = 0x7f03001d
com.hamode.appchat:attr/thickness = 0x7f030157
com.hamode.appchat:anim/abc_popup_enter = 0x7f010003
com.hamode.appchat:attr/theme = 0x7f030156
com.hamode.appchat:dimen/abc_text_size_display_4_material = 0x7f060043
com.hamode.appchat:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0f009e
com.hamode.appchat:attr/textAppearanceSearchResultSubtitle = 0x7f030150
com.hamode.appchat:attr/actionDropDownStyle = 0x7f03000c
com.hamode.appchat:string/common_google_play_services_updating_text = 0x7f0e0032
com.hamode.appchat:attr/drawableBottomCompat = 0x7f03007f
com.hamode.appchat:attr/textAppearanceListItem = 0x7f03014c
com.hamode.appchat:color/material_grey_100 = 0x7f050049
com.hamode.appchat:attr/progressBarStyle = 0x7f030108
com.hamode.appchat:styleable/StateListDrawable = 0x7f10003c
com.hamode.appchat:anim/abc_slide_out_top = 0x7f010009
com.hamode.appchat:attr/tag = 0x7f030149
com.hamode.appchat:drawable/abc_switch_track_mtrl_alpha = 0x7f070046
com.hamode.appchat:attr/buttonStyle = 0x7f03004a
com.hamode.appchat:attr/titleMargins = 0x7f030166
com.hamode.appchat:id/SYM = 0x7f080005
com.hamode.appchat:attr/entries = 0x7f030093
com.hamode.appchat:attr/switchTextOn = 0x7f030148
com.hamode.appchat:attr/dividerHorizontal = 0x7f03007c
com.hamode.appchat:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.hamode.appchat:attr/titleMarginEnd = 0x7f030163
com.hamode.appchat:attr/summary = 0x7f03013e
com.hamode.appchat:attr/textAppearanceListItemSecondary = 0x7f03014d
com.hamode.appchat:attr/titleTextColor = 0x7f030168
com.hamode.appchat:attr/closeIcon = 0x7f030053
com.hamode.appchat:attr/homeLayout = 0x7f0300b1
com.hamode.appchat:attr/subtitleTextColor = 0x7f03013b
com.hamode.appchat:bool/abc_allow_stacked_button_bar = 0x7f040001
com.hamode.appchat:attr/textColorAlertDialogListItem = 0x7f030153
com.hamode.appchat:attr/buttonBarNeutralButtonStyle = 0x7f030042
com.hamode.appchat:layout/abc_select_dialog_material = 0x7f0b001a
com.hamode.appchat:layout/abc_alert_dialog_material = 0x7f0b0009
com.hamode.appchat:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0f007c
com.hamode.appchat:drawable/ic_call_answer_video = 0x7f070076
com.hamode.appchat:attr/clearTop = 0x7f030052
com.hamode.appchat:attr/splitTrack = 0x7f030131
com.hamode.appchat:attr/splitMaxAspectRatioInPortrait = 0x7f03012c
com.hamode.appchat:attr/splitMaxAspectRatioInLandscape = 0x7f03012b
com.hamode.appchat:style/Base.Widget.AppCompat.TextView = 0x7f0f009d
com.hamode.appchat:attr/maxButtonHeight = 0x7f0300de
com.hamode.appchat:attr/scopeUris = 0x7f030111
com.hamode.appchat:attr/navigationContentDescription = 0x7f0300e5
com.hamode.appchat:xml/image_share_filepaths = 0x7f110000
com.hamode.appchat:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f060064
com.hamode.appchat:style/Widget.AppCompat.ActionBar.TabText = 0x7f0f0139
com.hamode.appchat:attr/textAppearanceListItemSmall = 0x7f03014e
com.hamode.appchat:attr/singleLineTitle = 0x7f030125
com.hamode.appchat:id/src_over = 0x7f0800b9
com.hamode.appchat:attr/showText = 0x7f030122
com.hamode.appchat:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.hamode.appchat:dimen/abc_text_size_title_material_toolbar = 0x7f06004d
com.hamode.appchat:attr/subtitle = 0x7f030139
com.hamode.appchat:style/Preference.DialogPreference = 0x7f0f00b3
com.hamode.appchat:attr/shouldDisableView = 0x7f03011e
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0f002f
com.hamode.appchat:attr/dropDownListViewStyle = 0x7f030089
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0f0102
com.hamode.appchat:drawable/abc_seekbar_track_material = 0x7f070042
com.hamode.appchat:string/v7_preference_on = 0x7f0e004b
com.hamode.appchat:id/bottom = 0x7f080048
com.hamode.appchat:style/TextAppearance.AppCompat.Button = 0x7f0f00e1
com.hamode.appchat:style/Base.Widget.AppCompat.PopupWindow = 0x7f0f0091
com.hamode.appchat:attr/selectableItemBackground = 0x7f03011b
com.hamode.appchat:attr/searchViewStyle = 0x7f030114
com.hamode.appchat:id/action_divider = 0x7f080030
com.hamode.appchat:drawable/abc_list_selector_holo_light = 0x7f070035
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0f0016
com.hamode.appchat:attr/switchPadding = 0x7f030142
com.hamode.appchat:attr/listPopupWindowStyle = 0x7f0300d4
com.hamode.appchat:attr/colorControlHighlight = 0x7f03005c
com.hamode.appchat:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0f0171
com.hamode.appchat:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0f000d
com.hamode.appchat:attr/colorButtonNormal = 0x7f03005a
com.hamode.appchat:animator/fragment_close_exit = 0x7f020001
com.hamode.appchat:attr/actionButtonStyle = 0x7f03000b
com.hamode.appchat:attr/ratingBarStyleSmall = 0x7f03010f
com.hamode.appchat:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0f00ec
com.hamode.appchat:attr/actionBarTabBarStyle = 0x7f030006
com.hamode.appchat:attr/firstBaselineToTopHeight = 0x7f03009e
com.hamode.appchat:color/bright_foreground_material_dark = 0x7f050024
com.hamode.appchat:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0f0093
com.hamode.appchat:attr/ratingBarStyleIndicator = 0x7f03010e
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0f00fd
com.hamode.appchat:layout/abc_list_menu_item_icon = 0x7f0b000f
com.hamode.appchat:attr/actionModeCopyDrawable = 0x7f030013
com.hamode.appchat:attr/preferenceFragmentStyle = 0x7f030100
com.hamode.appchat:color/abc_search_url_text_normal = 0x7f05000d
com.hamode.appchat:attr/listChoiceIndicatorSingleAnimated = 0x7f0300cf
com.hamode.appchat:attr/primaryActivityName = 0x7f030106
com.hamode.appchat:attr/gapBetweenBars = 0x7f0300ac
com.hamode.appchat:attr/stickyPlaceholder = 0x7f030136
com.hamode.appchat:attr/preserveIconSpacing = 0x7f030105
com.hamode.appchat:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0f0095
com.hamode.appchat:dimen/abc_progress_bar_height_material = 0x7f060035
com.hamode.appchat:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.hamode.appchat:attr/barLength = 0x7f03003e
com.hamode.appchat:attr/buttonBarStyle = 0x7f030044
com.hamode.appchat:id/off = 0x7f08008c
com.hamode.appchat:style/Base.V22.Theme.AppCompat = 0x7f0f0056
com.hamode.appchat:attr/actionBarTabTextStyle = 0x7f030008
com.hamode.appchat:attr/fontProviderPackage = 0x7f0300a5
com.hamode.appchat:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0f015e
com.hamode.appchat:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0f0096
com.hamode.appchat:attr/textAppearanceSmallPopupMenu = 0x7f030152
com.hamode.appchat:attr/textColorSearchUrl = 0x7f030154
com.hamode.appchat:attr/preferenceInformationStyle = 0x7f030101
com.hamode.appchat:attr/spinBars = 0x7f030127
com.hamode.appchat:style/Base.Widget.AppCompat.Button.Colored = 0x7f0f0076
com.hamode.appchat:color/abc_color_highlight_material = 0x7f050004
com.hamode.appchat:style/Widget.AppCompat.RatingBar = 0x7f0f0170
com.hamode.appchat:attr/editTextPreferenceStyle = 0x7f03008e
com.hamode.appchat:attr/preferenceFragmentListStyle = 0x7f0300ff
com.hamode.appchat:string/abc_menu_enter_shortcut_label = 0x7f0e000b
com.hamode.appchat:drawable/abc_ic_voice_search_api_material = 0x7f070027
com.hamode.appchat:color/browser_actions_text_color = 0x7f050028
com.hamode.appchat:attr/actionBarPopupTheme = 0x7f030002
com.hamode.appchat:attr/preferenceFragmentCompatStyle = 0x7f0300fe
com.hamode.appchat:id/fill_horizontal = 0x7f080068
com.hamode.appchat:drawable/abc_list_selector_disabled_holo_dark = 0x7f070032
com.hamode.appchat:attr/backgroundStacked = 0x7f03003b
com.hamode.appchat:animator/fragment_fade_exit = 0x7f020003
com.hamode.appchat:id/auto = 0x7f080045
com.hamode.appchat:color/foreground_material_dark = 0x7f05003f
com.hamode.appchat:attr/preferenceCategoryStyle = 0x7f0300fb
com.hamode.appchat:attr/checkedTextViewStyle = 0x7f030050
com.hamode.appchat:color/highlighted_text_material_light = 0x7f050042
com.hamode.appchat:styleable/AppCompatTextHelper = 0x7f10000e
com.hamode.appchat:attr/popupTheme = 0x7f0300f8
com.hamode.appchat:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.hamode.appchat:attr/panelMenuListWidth = 0x7f0300f4
com.hamode.appchat:color/abc_search_url_text = 0x7f05000c
com.hamode.appchat:attr/splitMinHeightDp = 0x7f03012d
com.hamode.appchat:attr/panelBackground = 0x7f0300f2
com.hamode.appchat:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0f008b
com.hamode.appchat:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070057
com.hamode.appchat:animator/fragment_open_exit = 0x7f020005
com.hamode.appchat:attr/listLayout = 0x7f0300d2
com.hamode.appchat:attr/overlapAnchor = 0x7f0300ed
com.hamode.appchat:id/search_mag_icon = 0x7f0800a8
com.hamode.appchat:attr/spinnerDropDownItemStyle = 0x7f030128
com.hamode.appchat:integer/cancel_button_image_alpha = 0x7f090002
com.hamode.appchat:attr/negativeButtonText = 0x7f0300e8
com.hamode.appchat:attr/alertDialogCenterButtons = 0x7f030026
com.hamode.appchat:drawable/googleg_standard_color_18 = 0x7f070072
com.hamode.appchat:attr/coordinatorLayoutStyle = 0x7f03006c
com.hamode.appchat:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0f0132
com.hamode.appchat:attr/titleMarginBottom = 0x7f030162
com.hamode.appchat:layout/abc_action_menu_item_layout = 0x7f0b0002
com.hamode.appchat:drawable/abc_list_selector_background_transition_holo_light = 0x7f070031
com.hamode.appchat:attr/drawableEndCompat = 0x7f030080
com.hamode.appchat:attr/splitMinWidthDp = 0x7f03012f
com.hamode.appchat:style/Preference.CheckBoxPreference = 0x7f0f00b1
com.hamode.appchat:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0049
com.hamode.appchat:attr/icon = 0x7f0300b2
com.hamode.appchat:attr/queryPatterns = 0x7f03010b
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0f010b
com.hamode.appchat:style/PreferenceThemeOverlay.v14.Material = 0x7f0f00cc
com.hamode.appchat:attr/tooltipForegroundColor = 0x7f03016c
com.hamode.appchat:style/TextAppearance.Compat.Notification.Info = 0x7f0f010f
com.hamode.appchat:id/accessibility_custom_action_31 = 0x7f080020
com.hamode.appchat:attr/layout_anchor = 0x7f0300c6
com.hamode.appchat:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.hamode.appchat:attr/min = 0x7f0300e3
com.hamode.appchat:style/Base.V7.Widget.AppCompat.EditText = 0x7f0f0065
com.hamode.appchat:attr/closeItemLayout = 0x7f030054
com.hamode.appchat:attr/measureWithLargestChild = 0x7f0300e1
com.hamode.appchat:style/Base.Widget.AppCompat.EditText = 0x7f0f0080
com.hamode.appchat:dimen/abc_action_button_min_height_material = 0x7f06000d
com.hamode.appchat:style/PreferenceFragment = 0x7f0f00c5
com.hamode.appchat:attr/textAppearancePopupMenuHeader = 0x7f03014f
com.hamode.appchat:attr/maxWidth = 0x7f0300e0
com.hamode.appchat:id/normal = 0x7f080088
com.hamode.appchat:id/never = 0x7f080086
com.hamode.appchat:attr/actionOverflowMenuStyle = 0x7f03001e
com.hamode.appchat:drawable/common_google_signin_btn_text_dark = 0x7f070068
com.hamode.appchat:id/checkbox = 0x7f080053
com.hamode.appchat:color/call_notification_answer_color = 0x7f05002c
com.hamode.appchat:attr/logo = 0x7f0300dc
com.hamode.appchat:attr/actionBarWidgetTheme = 0x7f03000a
com.hamode.appchat:string/abc_prepend_shortcut_label = 0x7f0e0011
com.hamode.appchat:attr/listPreferredItemPaddingEnd = 0x7f0300d8
com.hamode.appchat:attr/paddingStart = 0x7f0300f0
com.hamode.appchat:dimen/compat_button_padding_horizontal_material = 0x7f060052
com.hamode.appchat:style/Widget.AppCompat.ActionBar.Solid = 0x7f0f0137
com.hamode.appchat:attr/listPreferredItemHeightSmall = 0x7f0300d7
com.hamode.appchat:dimen/abc_text_size_body_2_material = 0x7f06003d
com.hamode.appchat:anim/abc_tooltip_exit = 0x7f01000b
com.hamode.appchat:attr/textLocale = 0x7f030155
com.hamode.appchat:attr/listPreferredItemHeightLarge = 0x7f0300d6
com.hamode.appchat:id/visible_removing_fragment_view_tag = 0x7f0800e5
com.hamode.appchat:attr/paddingTopNoTitle = 0x7f0300f1
com.hamode.appchat:attr/track = 0x7f03016f
com.hamode.appchat:attr/listChoiceBackgroundIndicator = 0x7f0300cd
com.hamode.appchat:id/fragment_container_view_tag = 0x7f08006b
com.hamode.appchat:attr/lineHeight = 0x7f0300cc
com.hamode.appchat:attr/backgroundTintMode = 0x7f03003d
com.hamode.appchat:attr/switchTextAppearance = 0x7f030146
com.hamode.appchat:attr/logoDescription = 0x7f0300dd
com.hamode.appchat:string/copy = 0x7f0e0037
com.hamode.appchat:attr/arrowHeadLength = 0x7f030031
com.hamode.appchat:attr/contentInsetRight = 0x7f030068
com.hamode.appchat:attr/switchMinWidth = 0x7f030141
com.hamode.appchat:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0f0127
com.hamode.appchat:attr/shortcutMatchRequired = 0x7f03011d
com.hamode.appchat:anim/abc_tooltip_enter = 0x7f01000a
com.hamode.appchat:attr/searchHintIcon = 0x7f030112
com.hamode.appchat:attr/singleChoiceItemLayout = 0x7f030124
com.hamode.appchat:attr/listMenuViewStyle = 0x7f0300d3
com.hamode.appchat:attr/layout_insetEdge = 0x7f0300ca
com.hamode.appchat:string/summary_collapsed_preference_list = 0x7f0e0049
com.hamode.appchat:drawable/abc_edit_text_material = 0x7f070014
com.hamode.appchat:id/browser_actions_menu_item_icon = 0x7f08004b
com.hamode.appchat:attr/enableCopying = 0x7f030091
com.hamode.appchat:attr/iconTintMode = 0x7f0300b5
com.hamode.appchat:attr/layoutManager = 0x7f0300c5
com.hamode.appchat:drawable/btn_radio_off_mtrl = 0x7f07005a
com.hamode.appchat:attr/dialogIcon = 0x7f030072
com.hamode.appchat:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0f0163
com.hamode.appchat:attr/lastBaselineToBottomHeight = 0x7f0300c3
com.hamode.appchat:attr/buttonTint = 0x7f03004c
com.hamode.appchat:attr/titleTextStyle = 0x7f030169
com.hamode.appchat:attr/tooltipFrameBackground = 0x7f03016d
com.hamode.appchat:attr/secondaryActivityAction = 0x7f030115
com.hamode.appchat:id/action_text = 0x7f080037
com.hamode.appchat:id/accessibility_custom_action_26 = 0x7f08001a
com.hamode.appchat:drawable/abc_seekbar_tick_mark_material = 0x7f070041
com.hamode.appchat:string/abc_menu_space_shortcut_label = 0x7f0e000f
com.hamode.appchat:dimen/item_touch_helper_swipe_escape_velocity = 0x7f060065
com.hamode.appchat:attr/key = 0x7f0300c0
com.hamode.appchat:attr/autoSizeStepGranularity = 0x7f030037
com.hamode.appchat:attr/imageButtonStyle = 0x7f0300b9
com.hamode.appchat:attr/dialogLayout = 0x7f030073
com.hamode.appchat:attr/windowFixedHeightMinor = 0x7f03017c
com.hamode.appchat:style/TextAppearance.AppCompat.Headline = 0x7f0f00e7
com.hamode.appchat:layout/preference_category = 0x7f0b002a
com.hamode.appchat:attr/iconTint = 0x7f0300b4
com.hamode.appchat:color/common_google_signin_btn_text_light_pressed = 0x7f050037
com.hamode.appchat:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0f0042
com.hamode.appchat:attr/dividerPadding = 0x7f03007d
com.hamode.appchat:string/common_google_play_services_wear_update_text = 0x7f0e0033
com.hamode.appchat:drawable/abc_ic_ab_back_material = 0x7f070015
com.hamode.appchat:drawable/abc_cab_background_internal_bg = 0x7f07000f
com.hamode.appchat:color/material_grey_600 = 0x7f05004c
com.hamode.appchat:attr/homeAsUpIndicator = 0x7f0300b0
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0f010a
com.hamode.appchat:attr/summaryOff = 0x7f03013f
com.hamode.appchat:attr/fontProviderFetchStrategy = 0x7f0300a3
com.hamode.appchat:string/abc_action_bar_home_description = 0x7f0e0000
com.hamode.appchat:attr/colorControlNormal = 0x7f03005d
com.hamode.appchat:attr/hideOnContentScroll = 0x7f0300af
com.hamode.appchat:attr/actionMenuTextColor = 0x7f03000f
com.hamode.appchat:layout/preference_category_material = 0x7f0b002b
com.hamode.appchat:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0f004d
com.hamode.appchat:attr/numericModifiers = 0x7f0300ea
com.hamode.appchat:drawable/abc_text_select_handle_right_mtrl_light = 0x7f07004f
com.hamode.appchat:attr/goIcon = 0x7f0300ad
com.hamode.appchat:attr/fontStyle = 0x7f0300a8
com.hamode.appchat:drawable/common_google_signin_btn_icon_light_normal = 0x7f070066
com.hamode.appchat:anim/abc_fade_out = 0x7f010001
com.hamode.appchat:attr/seekBarPreferenceStyle = 0x7f030118
com.hamode.appchat:attr/fontProviderSystemFontFamily = 0x7f0300a7
com.hamode.appchat:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0f00a7
com.hamode.appchat:attr/colorPrimary = 0x7f03005f
com.hamode.appchat:id/seekbar = 0x7f0800ac
com.hamode.appchat:drawable/notification_oversize_large_icon_bg = 0x7f070087
com.hamode.appchat:attr/fastScrollVerticalTrackDrawable = 0x7f03009a
com.hamode.appchat:attr/fontProviderCerts = 0x7f0300a2
com.hamode.appchat:attr/fastScrollVerticalThumbDrawable = 0x7f030099
com.hamode.appchat:style/ThemeOverlay.AppCompat.Light = 0x7f0f0135
com.hamode.appchat:attr/dialogTheme = 0x7f030077
com.hamode.appchat:drawable/abc_textfield_search_material = 0x7f070054
com.hamode.appchat:drawable/abc_btn_radio_material = 0x7f070009
com.hamode.appchat:attr/subMenuArrow = 0x7f030137
com.hamode.appchat:id/accessibility_custom_action_20 = 0x7f080014
com.hamode.appchat:attr/fastScrollHorizontalThumbDrawable = 0x7f030097
com.hamode.appchat:style/Widget.Compat.NotificationActionText = 0x7f0f0180
com.hamode.appchat:attr/alwaysExpand = 0x7f03002f
com.hamode.appchat:id/alwaysAllow = 0x7f080041
com.hamode.appchat:id/src_atop = 0x7f0800b7
com.hamode.appchat:color/notification_action_color_filter = 0x7f050050
com.hamode.appchat:integer/preferences_detail_pane_weight = 0x7f090005
com.hamode.appchat:attr/editTextBackground = 0x7f03008c
com.hamode.appchat:attr/displayOptions = 0x7f03007a
com.hamode.appchat:attr/drawerArrowStyle = 0x7f030088
com.hamode.appchat:string/call_notification_incoming_text = 0x7f0e0022
com.hamode.appchat:attr/paddingBottomNoButtons = 0x7f0300ee
com.hamode.appchat:attr/seekBarStyle = 0x7f030119
com.hamode.appchat:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0f015b
com.hamode.appchat:attr/drawableLeftCompat = 0x7f030081
com.hamode.appchat:dimen/disabled_alpha_material_light = 0x7f060058
com.hamode.appchat:dimen/abc_text_size_title_material = 0x7f06004c
com.hamode.appchat:attr/drawableStartCompat = 0x7f030084
com.hamode.appchat:attr/actionBarItemBackground = 0x7f030001
com.hamode.appchat:layout/expand_button = 0x7f0b001f
com.hamode.appchat:anim/abc_slide_in_bottom = 0x7f010006
com.hamode.appchat:color/material_blue_grey_950 = 0x7f050046
com.hamode.appchat:attr/windowActionModeOverlay = 0x7f03017a
com.hamode.appchat:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.hamode.appchat:attr/collapseContentDescription = 0x7f030055
com.hamode.appchat:attr/disableDependentsState = 0x7f030079
com.hamode.appchat:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0f008e
com.hamode.appchat:attr/dependency = 0x7f030070
com.hamode.appchat:id/textSpacerNoTitle = 0x7f0800d0
com.hamode.appchat:attr/switchStyle = 0x7f030145
com.hamode.appchat:attr/defaultValue = 0x7f03006f
com.hamode.appchat:attr/actionModeCloseButtonStyle = 0x7f030011
com.hamode.appchat:attr/fragment = 0x7f0300ab
com.hamode.appchat:id/icon_only = 0x7f080075
com.hamode.appchat:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.hamode.appchat:attr/actionModeBackground = 0x7f030010
com.hamode.appchat:style/Widget.AppCompat.ActionBar.TabView = 0x7f0f013a
com.hamode.appchat:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0800e2
com.hamode.appchat:id/disableHome = 0x7f080061
com.hamode.appchat:attr/seekBarIncrement = 0x7f030117
com.hamode.appchat:attr/backgroundSplit = 0x7f03003a
com.hamode.appchat:id/unchecked = 0x7f0800dd
com.hamode.appchat:id/scrollIndicatorDown = 0x7f08009f
com.hamode.appchat:attr/contentInsetEndWithActions = 0x7f030066
com.hamode.appchat:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.hamode.appchat:attr/tickMark = 0x7f03015b
com.hamode.appchat:attr/actionBarSplitStyle = 0x7f030004
com.hamode.appchat:attr/colorControlActivated = 0x7f03005b
com.hamode.appchat:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.hamode.appchat:attr/summaryOn = 0x7f030140
com.hamode.appchat:style/Widget.AppCompat.ActionBar = 0x7f0f0136
com.hamode.appchat:attr/contentDescription = 0x7f030064
com.hamode.appchat:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0f00fc
com.hamode.appchat:styleable/CoordinatorLayout_Layout = 0x7f100018
com.hamode.appchat:attr/positiveButtonText = 0x7f0300fa
com.hamode.appchat:attr/order = 0x7f0300eb
com.hamode.appchat:attr/colorSwitchThumbNormal = 0x7f030062
com.hamode.appchat:drawable/abc_cab_background_top_material = 0x7f070010
com.hamode.appchat:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0f0070
com.hamode.appchat:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.hamode.appchat:attr/colorPrimaryDark = 0x7f030060
com.hamode.appchat:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.hamode.appchat:attr/colorError = 0x7f03005e
com.hamode.appchat:attr/listItemLayout = 0x7f0300d1
com.hamode.appchat:attr/color = 0x7f030057
com.hamode.appchat:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.hamode.appchat:attr/preferenceCategoryTitleTextColor = 0x7f0300fd
com.hamode.appchat:attr/contentInsetStart = 0x7f030069
com.hamode.appchat:id/tag_window_insets_animation_callback = 0x7f0800cc
com.hamode.appchat:attr/windowMinWidthMinor = 0x7f030180
com.hamode.appchat:attr/actionBarTabStyle = 0x7f030007
com.hamode.appchat:attr/colorScheme = 0x7f030061
com.hamode.appchat:attr/dialogMessage = 0x7f030074
com.hamode.appchat:dimen/abc_button_inset_vertical_material = 0x7f060013
com.hamode.appchat:layout/notification_action = 0x7f0b0023
com.hamode.appchat:attr/buttonTintMode = 0x7f03004d
com.hamode.appchat:attr/showSeekBarValue = 0x7f030121
com.hamode.appchat:drawable/notification_icon_background = 0x7f070086
com.hamode.appchat:style/Preference.PreferenceScreen.Material = 0x7f0f00bd
com.hamode.appchat:attr/buttonBarButtonStyle = 0x7f030040
com.hamode.appchat:id/recycler_view = 0x7f080096
com.hamode.appchat:attr/autoSizeMaxTextSize = 0x7f030034
com.hamode.appchat:style/Theme.AppCompat.DayNight.Dialog = 0x7f0f011a
com.hamode.appchat:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0f005c
com.hamode.appchat:attr/dialogTitle = 0x7f030078
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0f0152
com.hamode.appchat:drawable/abc_text_select_handle_middle_mtrl_dark = 0x7f07004c
com.hamode.appchat:style/PreferenceFragmentList = 0x7f0f00c7
com.hamode.appchat:attr/buttonCompat = 0x7f030045
com.hamode.appchat:dimen/abc_search_view_preferred_width = 0x7f060037
com.hamode.appchat:string/common_google_play_services_install_text = 0x7f0e0029
com.hamode.appchat:attr/titleTextAppearance = 0x7f030167
com.hamode.appchat:dimen/notification_media_narrow_margin = 0x7f06006d
com.hamode.appchat:attr/fastScrollHorizontalTrackDrawable = 0x7f030098
com.hamode.appchat:attr/actionModePopupWindowStyle = 0x7f030017
com.hamode.appchat:string/abc_action_bar_up_description = 0x7f0e0001
com.hamode.appchat:id/start = 0x7f0800bb
com.hamode.appchat:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.hamode.appchat:layout/select_dialog_multichoice_material = 0x7f0b003a
com.hamode.appchat:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.hamode.appchat:style/TextAppearance.AppCompat.Large = 0x7f0f00e9
com.hamode.appchat:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.hamode.appchat:attr/divider = 0x7f03007b
com.hamode.appchat:attr/layout_dodgeInsetEdges = 0x7f0300c9
com.hamode.appchat:attr/alertDialogButtonGroupStyle = 0x7f030025
com.hamode.appchat:color/material_grey_900 = 0x7f05004f
com.hamode.appchat:style/Preference.CheckBoxPreference.Material = 0x7f0f00b2
com.hamode.appchat:color/material_deep_teal_500 = 0x7f050048
com.hamode.appchat:attr/commitIcon = 0x7f030063
com.hamode.appchat:attr/borderlessButtonStyle = 0x7f03003f
com.hamode.appchat:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.hamode.appchat:attr/actionModeCutDrawable = 0x7f030014
com.hamode.appchat:drawable/btn_checkbox_checked_mtrl = 0x7f070056
com.hamode.appchat:layout/preference_material = 0x7f0b0032
com.hamode.appchat:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.hamode.appchat:attr/maxHeight = 0x7f0300df
com.hamode.appchat:attr/autoCompleteTextViewStyle = 0x7f030033
com.hamode.appchat:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.hamode.appchat:id/view_tree_saved_state_registry_owner = 0x7f0800e3
com.hamode.appchat:drawable/abc_ic_star_black_16dp = 0x7f070021
com.hamode.appchat:attr/autoSizeTextType = 0x7f030038
com.hamode.appchat:id/checked = 0x7f080054
com.hamode.appchat:attr/allowStacking = 0x7f03002c
com.hamode.appchat:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0f0128
com.hamode.appchat:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.hamode.appchat:attr/expandActivityOverflowButtonDrawable = 0x7f030095
com.hamode.appchat:styleable/MenuItem = 0x7f100028
com.hamode.appchat:attr/listPreferredItemPaddingStart = 0x7f0300db
com.hamode.appchat:attr/checkboxStyle = 0x7f03004f
com.hamode.appchat:attr/layout_anchorGravity = 0x7f0300c7
com.hamode.appchat:attr/contentInsetStartWithNavigation = 0x7f03006a
com.hamode.appchat:color/abc_tint_spinner = 0x7f050016
com.hamode.appchat:string/abc_toolbar_collapse_description = 0x7f0e001a
com.hamode.appchat:id/bottomToTop = 0x7f080049
com.hamode.appchat:dimen/browser_actions_context_menu_max_width = 0x7f06004e
com.hamode.appchat:style/Widget.AppCompat.EditText = 0x7f0f014e
com.hamode.appchat:layout/preference_widget_checkbox = 0x7f0b0034
com.hamode.appchat:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070053
com.hamode.appchat:id/special_effects_controller_view_tag = 0x7f0800b4
com.hamode.appchat:attr/alertDialogStyle = 0x7f030027
com.hamode.appchat:style/Base.V26.Theme.AppCompat = 0x7f0f005a
com.hamode.appchat:string/abc_activitychooserview_choose_application = 0x7f0e0005
com.hamode.appchat:id/accessibility_custom_action_1 = 0x7f080008
com.hamode.appchat:attr/placeholderActivityName = 0x7f0300f6
com.hamode.appchat:string/common_google_play_services_notification_channel_name = 0x7f0e002b
com.hamode.appchat:style/PreferenceThemeOverlay = 0x7f0f00ca
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar = 0x7f0f0150
com.hamode.appchat:anim/abc_slide_out_bottom = 0x7f010008
com.hamode.appchat:attr/statusBarBackground = 0x7f030135
com.hamode.appchat:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.hamode.appchat:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0f0176
com.hamode.appchat:dimen/abc_text_size_subhead_material = 0x7f06004a
com.hamode.appchat:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070011
com.hamode.appchat:id/accessibility_custom_action_29 = 0x7f08001d
com.hamode.appchat:attr/activityChooserViewStyle = 0x7f030022
com.hamode.appchat:drawable/abc_list_selector_holo_dark = 0x7f070034
com.hamode.appchat:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0f0143
com.hamode.appchat:id/search_bar = 0x7f0800a3
com.hamode.appchat:id/submenuarrow = 0x7f0800bc
com.hamode.appchat:drawable/notification_bg = 0x7f070080
com.hamode.appchat:attr/allowDividerAfterLastItem = 0x7f03002a
com.hamode.appchat:attr/indeterminateProgressStyle = 0x7f0300ba
com.hamode.appchat:layout/ime_secondary_split_test_activity = 0x7f0b0022
com.hamode.appchat:attr/actionViewClass = 0x7f030020
com.hamode.appchat:style/Widget.AppCompat.RatingBar.Small = 0x7f0f0172
com.hamode.appchat:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.hamode.appchat:attr/useSimpleSummaryProvider = 0x7f030174
com.hamode.appchat:attr/editTextColor = 0x7f03008d
com.hamode.appchat:string/common_signin_button_text_long = 0x7f0e0036
com.hamode.appchat:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070050
com.hamode.appchat:attr/actionModeStyle = 0x7f03001b
com.hamode.appchat:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0f0126
com.hamode.appchat:attr/actionModeShareDrawable = 0x7f030019
com.hamode.appchat:attr/actionModeFindDrawable = 0x7f030015
com.hamode.appchat:id/buttonPanel = 0x7f08004f
com.hamode.appchat:attr/alpha = 0x7f03002d
com.hamode.appchat:attr/actionModePasteDrawable = 0x7f030016
com.hamode.appchat:id/transition_transform = 0x7f0800dc
com.hamode.appchat:drawable/abc_ic_star_half_black_48dp = 0x7f070026
com.hamode.appchat:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.hamode.appchat:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.hamode.appchat:attr/actionModeCloseDrawable = 0x7f030012
com.hamode.appchat:attr/editTextStyle = 0x7f03008f
com.hamode.appchat:styleable/AppCompatSeekBar = 0x7f10000d
com.hamode.appchat:id/accessibility_custom_action_6 = 0x7f080023
com.hamode.appchat:attr/iconifiedByDefault = 0x7f0300b6
com.hamode.appchat:drawable/common_google_signin_btn_icon_light = 0x7f070064
com.hamode.appchat:attr/autoSizePresetSizes = 0x7f030036
com.hamode.appchat:attr/preferenceCategoryTitleTextAppearance = 0x7f0300fc
com.hamode.appchat:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0f0066
com.hamode.appchat:attr/preferenceTheme = 0x7f030104
com.hamode.appchat:id/up = 0x7f0800df
com.hamode.appchat:attr/animationBackgroundColor = 0x7f030030
com.hamode.appchat:anim/abc_fade_in = 0x7f010000
com.hamode.appchat:id/accessibility_custom_action_16 = 0x7f08000f
com.hamode.appchat:attr/splitLayoutDirection = 0x7f03012a
com.hamode.appchat:attr/actionBarDivider = 0x7f030000
com.hamode.appchat:style/Base.Theme.AppCompat = 0x7f0f003c
com.hamode.appchat:attr/actionProviderClass = 0x7f03001f
com.hamode.appchat:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0f0151
com.hamode.appchat:attr/fastScrollEnabled = 0x7f030096
com.hamode.appchat:attr/submitBackground = 0x7f030138
com.hamode.appchat:attr/suggestionRowLayout = 0x7f03013d
com.hamode.appchat:attr/stackFromEnd = 0x7f030133
com.hamode.appchat:attr/checkBoxPreferenceStyle = 0x7f03004e
com.hamode.appchat:layout/abc_dialog_title_material = 0x7f0b000c
com.hamode.appchat:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.hamode.appchat:color/common_google_signin_btn_text_light_focused = 0x7f050036
com.hamode.appchat:attr/listDividerAlertDialog = 0x7f0300d0
com.hamode.appchat:attr/buttonStyleSmall = 0x7f03004b
com.hamode.appchat:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f07005d
com.hamode.appchat:color/button_material_dark = 0x7f05002a
com.hamode.appchat:dimen/abc_text_size_large_material = 0x7f060045
com.hamode.appchat:attr/menu = 0x7f0300e2
com.hamode.appchat:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0f010d
com.hamode.appchat:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.hamode.appchat:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0f0087
com.hamode.appchat:attr/actionLayout = 0x7f03000d
com.hamode.appchat:style/Widget.AppCompat.ActivityChooserView = 0x7f0f013f
com.hamode.appchat:attr/buttonBarPositiveButtonStyle = 0x7f030043
com.hamode.appchat:attr/persistent = 0x7f0300f5
com.hamode.appchat:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0f0161
com.hamode.appchat:attr/textAppearanceSearchResultTitle = 0x7f030151
com.hamode.appchat:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.hamode.appchat:style/Platform.ThemeOverlay.AppCompat = 0x7f0f00a6
com.hamode.appchat:attr/background = 0x7f030039
com.hamode.appchat:styleable/ActionMode = 0x7f100004
com.hamode.appchat:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0f00f2
com.hamode.appchat:attr/autoSizeMinTextSize = 0x7f030035
