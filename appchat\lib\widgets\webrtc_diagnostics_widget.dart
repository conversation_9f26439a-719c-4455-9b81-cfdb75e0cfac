import 'dart:async';
import 'package:flutter/material.dart';
import '../services/webrtc_firestore_room_service.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

/// A widget that displays diagnostic information about WebRTC connections
///
/// This is useful for debugging WebRTC issues in the app
class WebRTCDiagnosticsWidget extends StatefulWidget {
  final WebRTCFirestoreRoomService webRTCService;

  const WebRTCDiagnosticsWidget({super.key, required this.webRTCService});

  @override
  State<WebRTCDiagnosticsWidget> createState() =>
      _WebRTCDiagnosticsWidgetState();
}

class _WebRTCDiagnosticsWidgetState extends State<WebRTCDiagnosticsWidget> {
  Map<String, dynamic> _diagnosticInfo = {};
  Timer? _refreshTimer;

  @override
  void initState() {
    super.initState();
    _updateDiagnosticInfo();

    // Refresh diagnostic info every 2 seconds
    _refreshTimer = Timer.periodic(const Duration(seconds: 2), (_) {
      _updateDiagnosticInfo();
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  void _updateDiagnosticInfo() {
    setState(() {
      _diagnosticInfo = widget.webRTCService.getDiagnosticInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'WebRTC Diagnostics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const SizedBox(height: 8),

            // Connection status with colored indicators
            _buildStatusRow(
              'Call Active',
              _diagnosticInfo['isCallActive'] == true ? 'Yes' : 'No',
              valueColor:
                  _diagnosticInfo['isCallActive'] == true
                      ? Colors.green
                      : Colors.red,
            ),
            _buildStatusRow(
              'Call ID',
              _diagnosticInfo['callId']?.toString() ?? 'None',
            ),
            _buildStatusRow(
              'Remote User',
              _diagnosticInfo['remoteUserId']?.toString() ?? 'None',
            ),

            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),

            // Connection states with colored indicators
            _buildConnectionStateRow(
              'Connection State',
              _diagnosticInfo['connectionState']?.toString() ?? 'Unknown',
            ),
            _buildIceConnectionStateRow(
              'ICE Connection',
              _diagnosticInfo['iceConnectionState']?.toString() ?? 'Unknown',
            ),
            _buildStatusRow(
              'ICE Gathering',
              _diagnosticInfo['iceGatheringState']?.toString() ?? 'Unknown',
            ),
            _buildStatusRow(
              'Signaling State',
              _diagnosticInfo['signalingState']?.toString() ?? 'Unknown',
            ),

            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),

            // Media status with colored indicators
            _buildStatusRow(
              'Local Stream',
              _diagnosticInfo['localStreamActive'] == true
                  ? 'Active'
                  : 'Inactive',
              valueColor:
                  _diagnosticInfo['localStreamActive'] == true
                      ? Colors.green
                      : Colors.red,
            ),
            _buildStatusRow(
              'Remote Stream',
              _diagnosticInfo['remoteStreamActive'] == true
                  ? 'Active'
                  : 'Inactive',
              valueColor:
                  _diagnosticInfo['remoteStreamActive'] == true
                      ? Colors.green
                      : Colors.red,
            ),
            _buildStatusRow(
              'Audio Enabled',
              _diagnosticInfo['audioEnabled'] == true ? 'Yes' : 'No (Muted)',
              valueColor:
                  _diagnosticInfo['audioEnabled'] == true
                      ? Colors.green
                      : Colors.orange,
            ),

            const SizedBox(height: 8),
            const Divider(),
            const SizedBox(height: 8),

            // Connection details
            _buildStatusRow(
              'Connection Attempts',
              _diagnosticInfo['connectionAttempts']?.toString() ?? '0',
              valueColor:
                  (_diagnosticInfo['connectionAttempts'] ?? 0) > 0
                      ? Colors.orange
                      : null,
            ),
            _buildStatusRow(
              'ICE Transport Policy',
              _diagnosticInfo['iceTransportPolicy']?.toString() ?? 'Unknown',
              valueColor:
                  _diagnosticInfo['iceTransportPolicy'] == 'relay'
                      ? Colors.orange
                      : Colors.green,
            ),
            _buildStatusRow(
              'ICE Servers',
              _diagnosticInfo['iceServersCount']?.toString() ?? '0',
              valueColor:
                  (_diagnosticInfo['iceServersCount'] ?? 0) > 5
                      ? Colors.green
                      : Colors.orange,
            ),

            const SizedBox(height: 16),

            // Network connectivity status
            FutureBuilder<bool>(
              future: _checkInternetConnectivity(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }

                final hasConnectivity = snapshot.data ?? false;
                return _buildStatusRow(
                  'Internet Connectivity',
                  hasConnectivity ? 'Available' : 'Not Available',
                  valueColor: hasConnectivity ? Colors.green : Colors.red,
                );
              },
            ),

            const SizedBox(height: 16),

            // Refresh button
            Center(
              child: ElevatedButton.icon(
                onPressed: _updateDiagnosticInfo,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Check if the device has internet connectivity
  Future<bool> _checkInternetConnectivity() async {
    try {
      // Use the internet_connection_checker package to check connectivity
      return await InternetConnectionChecker.instance.hasConnection;
    } catch (e) {
      debugPrint('Error checking internet connectivity: $e');
      return false;
    }
  }

  /// Build a connection state row with appropriate color based on state
  Widget _buildConnectionStateRow(String label, String value) {
    Color? valueColor;

    // Determine color based on connection state
    if (value.contains('Connected')) {
      valueColor = Colors.green;
    } else if (value.contains('Connecting') || value.contains('New')) {
      valueColor = Colors.blue;
    } else if (value.contains('Disconnected') || value.contains('Closed')) {
      valueColor = Colors.red;
    } else if (value.contains('Failed')) {
      valueColor = Colors.red;
    }

    return _buildStatusRow(label, value, valueColor: valueColor);
  }

  /// Build an ICE connection state row with appropriate color based on state
  Widget _buildIceConnectionStateRow(String label, String value) {
    Color? valueColor;

    // Determine color based on ICE connection state
    if (value.contains('Connected') || value.contains('Completed')) {
      valueColor = Colors.green;
    } else if (value.contains('Checking')) {
      valueColor = Colors.blue;
    } else if (value.contains('Disconnected')) {
      valueColor = Colors.orange;
    } else if (value.contains('Failed')) {
      valueColor = Colors.red;
    }

    return _buildStatusRow(label, value, valueColor: valueColor);
  }

  Widget _buildStatusRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
          Text(value, style: TextStyle(color: valueColor)),
        ],
      ),
    );
  }
}
