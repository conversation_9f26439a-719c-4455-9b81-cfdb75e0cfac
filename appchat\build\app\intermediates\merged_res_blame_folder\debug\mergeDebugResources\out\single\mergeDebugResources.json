[{"merged": "com.hamode.appchat.app-debug-53:/mipmap-xhdpi_ic_launcher.png.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-xhdpi/ic_launcher.png"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-hdpi_ic_launcher_foreground.png.flat", "source": "com.hamode.appchat.app-main-47:/drawable-hdpi/ic_launcher_foreground.png"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-xxhdpi_ic_launcher_foreground.png.flat", "source": "com.hamode.appchat.app-main-47:/drawable-xxhdpi/ic_launcher_foreground.png"}, {"merged": "com.hamode.appchat.app-debug-53:/mipmap-mdpi_ic_launcher.png.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-mdpi/ic_launcher.png"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-mdpi_ic_launcher_foreground.png.flat", "source": "com.hamode.appchat.app-main-47:/drawable-mdpi/ic_launcher_foreground.png"}, {"merged": "com.hamode.appchat.app-debug-53:/mipmap-xxhdpi_ic_launcher.png.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-xxhdpi/ic_launcher.png"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-v21_launch_background.xml.flat", "source": "com.hamode.appchat.app-main-47:/drawable-v21/launch_background.xml"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-xhdpi_ic_launcher_foreground.png.flat", "source": "com.hamode.appchat.app-main-47:/drawable-xhdpi/ic_launcher_foreground.png"}, {"merged": "com.hamode.appchat.app-debug-53:/mipmap-xxxhdpi_ic_launcher.png.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-xxxhdpi/ic_launcher.png"}, {"merged": "com.hamode.appchat.app-debug-53:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.hamode.appchat.app-debug-53:/drawable-xxxhdpi_ic_launcher_foreground.png.flat", "source": "com.hamode.appchat.app-main-47:/drawable-xxxhdpi/ic_launcher_foreground.png"}, {"merged": "com.hamode.appchat.app-debug-53:/mipmap-hdpi_ic_launcher.png.flat", "source": "com.hamode.appchat.app-main-47:/mipmap-hdpi/ic_launcher.png"}]