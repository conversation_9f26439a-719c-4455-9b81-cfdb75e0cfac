import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import '../firebase_options.dart';

/// This script initializes the Firestore collections needed for WebRTC.
/// 
/// Run this script once to set up the necessary collections and documents
/// for WebRTC signaling to work properly.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  runApp(const InitializeFirestoreApp());
}

class InitializeFirestoreApp extends StatelessWidget {
  const InitializeFirestoreApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Initialize Firestore for WebRTC'),
        ),
        body: const InitializeFirestoreScreen(),
      ),
    );
  }
}

class InitializeFirestoreScreen extends StatefulWidget {
  const InitializeFirestoreScreen({super.key});

  @override
  State<InitializeFirestoreScreen> createState() => _InitializeFirestoreScreenState();
}

class _InitializeFirestoreScreenState extends State<InitializeFirestoreScreen> {
  bool _isInitializing = false;
  String _status = 'Ready to initialize Firestore for WebRTC';
  final List<String> _logs = [];

  Future<void> _initializeFirestore() async {
    setState(() {
      _isInitializing = true;
      _status = 'Initializing Firestore...';
      _logs.add('Starting initialization...');
    });

    try {
      final firestore = FirebaseFirestore.instance;
      
      // Create rooms collection
      _addLog('Creating rooms collection...');
      
      // Create a test room document
      final roomRef = firestore.collection('rooms').doc('test_room');
      await roomRef.set({
        'createdAt': FieldValue.serverTimestamp(),
        'createdBy': 'system',
        'isActive': true,
        'description': 'Test room for WebRTC',
      });
      _addLog('Created test room document');
      
      // Create participants subcollection
      await roomRef.collection('participants').doc('system').set({
        'joinedAt': FieldValue.serverTimestamp(),
        'isActive': true,
        'role': 'system',
      });
      _addLog('Created participants subcollection');
      
      // Create signaling subcollection
      await roomRef.collection('signaling').add({
        'type': 'test',
        'senderId': 'system',
        'timestamp': FieldValue.serverTimestamp(),
        'message': 'Test signaling message',
      });
      _addLog('Created signaling subcollection');
      
      // Create candidates subcollection
      await roomRef.collection('candidates').add({
        'candidate': 'test candidate',
        'sdpMid': 'test',
        'sdpMLineIndex': 0,
        'senderId': 'system',
        'timestamp': FieldValue.serverTimestamp(),
      });
      _addLog('Created candidates subcollection');
      
      // Create activeCalls collection
      _addLog('Creating activeCalls collection...');
      await firestore.collection('activeCalls').doc('test_call').set({
        'caller': 'system',
        'receiver': 'system',
        'startTime': FieldValue.serverTimestamp(),
        'isActive': true,
      });
      _addLog('Created activeCalls collection');
      
      // Create callMatching collection
      _addLog('Creating callMatching collection...');
      await firestore.collection('callMatching').doc('test_match').set({
        'userId': 'system',
        'preferences': {
          'gender': 'any',
        },
        'timestamp': FieldValue.serverTimestamp(),
      });
      _addLog('Created callMatching collection');
      
      setState(() {
        _status = 'Firestore initialized successfully!';
        _logs.add('Initialization complete!');
      });
    } catch (e) {
      setState(() {
        _status = 'Error initializing Firestore: $e';
        _logs.add('Error: $e');
      });
    } finally {
      setState(() {
        _isInitializing = false;
      });
    }
  }
  
  void _addLog(String log) {
    setState(() {
      _logs.add(log);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _status,
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _isInitializing ? null : _initializeFirestore,
            child: const Text('Initialize Firestore'),
          ),
          const SizedBox(height: 16),
          const Text(
            'Logs:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: _logs.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4.0),
                  child: Text('${index + 1}. ${_logs[index]}'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
