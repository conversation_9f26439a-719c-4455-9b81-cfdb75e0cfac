{"logs": [{"outputFile": "com.hamode.appchat.app-mergeDebugResources-51:/values-lt/values-lt.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\04f854c86687d4911c82dc9952d1fb33\\transformed\\jetified-play-services-base-18.5.0\\res\\values-lt\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,444,572,675,824,950,1065,1167,1329,1434,1595,1725,1874,2020,2084,2146", "endColumns": "102,147,127,102,148,125,114,101,161,104,160,129,148,145,63,61,85", "endOffsets": "295,443,571,674,823,949,1064,1166,1328,1433,1594,1724,1873,2019,2083,2145,2231"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3854,3961,4113,4245,4352,4505,4635,4754,5019,5185,5294,5459,5593,5746,5896,5964,6030", "endColumns": "106,151,131,106,152,129,118,105,165,108,164,133,152,149,67,65,89", "endOffsets": "3956,4108,4240,4347,4500,4630,4749,4855,5180,5289,5454,5588,5741,5891,5959,6025,6115"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\70d9edd68c4ac01d9150f2de0a6ef51d\\transformed\\browser-1.4.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,265,379", "endColumns": "104,104,113,105", "endOffsets": "155,260,374,480"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6193,6389,6494,6608", "endColumns": "104,104,113,105", "endOffsets": "6293,6489,6603,6709"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ab37722a56b48a618e91dc4d4ff760f9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,113", "endOffsets": "165,279"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2874,2989", "endColumns": "114,113", "endOffsets": "2984,3098"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9ecd58143158fe7544d1e8fee291fc86\\transformed\\core-1.13.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,263,362,465,576,686,806", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "148,258,357,460,571,681,801,902"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3103,3201,3311,3410,3513,3624,3734,7021", "endColumns": "97,109,98,102,110,109,119,100", "endOffsets": "3196,3306,3405,3508,3619,3729,3849,7117"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\0a2895bad47a5ad7be3689da4d9dc4ce\\transformed\\appcompat-1.1.0\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,913,1007,1102,1199,1295,1399,1495,1593,1689,1783,1877,1959,2068,2176,2276,2386,2491,2597,2773,2874", "endColumns": "115,103,112,86,101,121,82,80,93,94,96,95,103,95,97,95,93,93,81,108,107,99,109,104,105,175,100,82", "endOffsets": "216,320,433,520,622,744,827,908,1002,1097,1194,1290,1394,1490,1588,1684,1778,1872,1954,2063,2171,2271,2381,2486,2592,2768,2869,2952"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,221,325,438,525,627,749,832,913,1007,1102,1199,1295,1399,1495,1593,1689,1783,1877,1959,2068,2176,2276,2386,2491,2597,2773,6938", "endColumns": "115,103,112,86,101,121,82,80,93,94,96,95,103,95,97,95,93,93,81,108,107,99,109,104,105,175,100,82", "endOffsets": "216,320,433,520,622,744,827,908,1002,1097,1194,1290,1394,1490,1588,1684,1778,1872,1954,2063,2171,2271,2381,2486,2592,2768,2869,7016"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5cbc467f2278e032b60487f8f70af125\\transformed\\preference-1.2.1\\res\\values-lt\\values-lt.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,178,269,347,493,662,747", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "173,264,342,488,657,742,823"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6120,6298,6714,6792,7122,7291,7376", "endColumns": "72,90,77,145,168,84,80", "endOffsets": "6188,6384,6787,6933,7286,7371,7452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\82766f43c900f418c4a97a171253098c\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-lt\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "154", "endOffsets": "349"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4860", "endColumns": "158", "endOffsets": "5014"}}]}]}