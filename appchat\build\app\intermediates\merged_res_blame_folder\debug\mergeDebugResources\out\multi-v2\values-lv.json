{"logs": [{"outputFile": "com.hamode.appchat.app-mergeDebugResources-51:/values-lv/values-lv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\9ecd58143158fe7544d1e8fee291fc86\\transformed\\core-1.13.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,563,671,786", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "148,250,350,451,558,666,781,882"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3223,3321,3423,3523,3624,3731,3839,7153", "endColumns": "97,101,99,100,106,107,114,100", "endOffsets": "3316,3418,3518,3619,3726,3834,3949,7249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\70d9edd68c4ac01d9150f2de0a6ef51d\\transformed\\browser-1.4.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,158,257,373", "endColumns": "102,98,115,101", "endOffsets": "153,252,368,470"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6327,6528,6627,6743", "endColumns": "102,98,115,101", "endOffsets": "6425,6622,6738,6840"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\82766f43c900f418c4a97a171253098c\\transformed\\jetified-play-services-basement-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "161", "endOffsets": "356"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4959", "endColumns": "165", "endOffsets": "5120"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\04f854c86687d4911c82dc9952d1fb33\\transformed\\jetified-play-services-base-18.5.0\\res\\values-lv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,453,582,686,824,951,1064,1166,1337,1442,1607,1738,1903,2054,2114,2178", "endColumns": "102,156,128,103,137,126,112,101,170,104,164,130,164,150,59,63,84", "endOffsets": "295,452,581,685,823,950,1063,1165,1336,1441,1606,1737,1902,2053,2113,2177,2262"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3954,4061,4222,4355,4463,4605,4736,4853,5125,5300,5409,5578,5713,5882,6037,6101,6169", "endColumns": "106,160,132,107,141,130,116,105,174,108,168,134,168,154,63,67,88", "endOffsets": "4056,4217,4350,4458,4600,4731,4848,4954,5295,5404,5573,5708,5877,6032,6096,6164,6253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\5cbc467f2278e032b60487f8f70af125\\transformed\\preference-1.2.1\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,272,352,498,667,752", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "169,267,347,493,662,747,829"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6258,6430,6845,6925,7254,7423,7508", "endColumns": "68,97,79,145,168,84,81", "endOffsets": "6322,6523,6920,7066,7418,7503,7585"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\0a2895bad47a5ad7be3689da4d9dc4ce\\transformed\\appcompat-1.1.0\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,919,1029,1136,1242,1351,1463,1566,1678,1785,1890,1990,2074,2183,2294,2393,2504,2611,2716,2890,2989", "endColumns": "119,109,108,85,103,121,81,80,109,106,105,108,111,102,111,106,104,99,83,108,110,98,110,106,104,173,98,81", "endOffsets": "220,330,439,525,629,751,833,914,1024,1131,1237,1346,1458,1561,1673,1780,1885,1985,2069,2178,2289,2388,2499,2606,2711,2885,2984,3066"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,335,444,530,634,756,838,919,1029,1136,1242,1351,1463,1566,1678,1785,1890,1990,2074,2183,2294,2393,2504,2611,2716,2890,7071", "endColumns": "119,109,108,85,103,121,81,80,109,106,105,108,111,102,111,106,104,99,83,108,110,98,110,106,104,173,98,81", "endOffsets": "220,330,439,525,629,751,833,914,1024,1131,1237,1346,1458,1561,1673,1780,1885,1985,2069,2178,2289,2388,2499,2606,2711,2885,2984,7148"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\ab37722a56b48a618e91dc4d4ff760f9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-lv\\values-lv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,123", "endOffsets": "160,284"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2989,3099", "endColumns": "109,123", "endOffsets": "3094,3218"}}]}]}