import 'package:flutter/material.dart';
import '../services/service_provider.dart';

/// A test widget for verifying WebRTC functionality
///
/// This widget can be used to test WebRTC audio calls without
/// going through the full app flow. It allows testing of:
/// - WebRTC connection establishment
/// - Audio streaming
/// - Mute/unmute functionality
/// - Speaker control
class WebRTCTestScreen extends StatefulWidget {
  const WebRTCTestScreen({super.key});

  @override
  State<WebRTCTestScreen> createState() => _WebRTCTestScreenState();
}

class _WebRTCTestScreenState extends State<WebRTCTestScreen> {
  final _serviceProvider = ServiceProvider();
  final _roomIdController = TextEditingController();
  final _remoteUserIdController = TextEditingController();

  String _callStatus = 'idle';
  bool _isMuted = false;
  bool _isSpeakerOn = false;
  bool _isInitiator = true;

  @override
  void initState() {
    super.initState();
    _setupCallStateListener();
  }

  void _setupCallStateListener() {
    _serviceProvider.webRTCService.callStateStream.listen((state) {
      setState(() {
        _callStatus = state;
      });
    });
  }

  Future<void> _startCall() async {
    if (_roomIdController.text.isEmpty) {
      _showError('Room ID is required');
      return;
    }

    if (_remoteUserIdController.text.isEmpty) {
      _showError('Remote User ID is required');
      return;
    }

    try {
      await _serviceProvider.webRTCService.initializeCall(
        _roomIdController.text,
        _remoteUserIdController.text,
        _isInitiator,
      );

      setState(() {
        _callStatus = 'connecting';
      });
    } catch (e) {
      _showError('Failed to start call: $e');
    }
  }

  Future<void> _endCall() async {
    try {
      await _serviceProvider.webRTCService.endCall();
      setState(() {
        _callStatus = 'idle';
      });
    } catch (e) {
      _showError('Failed to end call: $e');
    }
  }

  void _toggleMute() {
    setState(() {
      _isMuted = !_isMuted;
      _serviceProvider.webRTCService.setMicrophoneMuted(_isMuted);
    });
  }

  void _toggleSpeaker() {
    setState(() {
      _isSpeakerOn = !_isSpeakerOn;
      _serviceProvider.webRTCService.setSpeakerEnabled(_isSpeakerOn);
    });
  }

  void _toggleInitiator() {
    setState(() {
      _isInitiator = !_isInitiator;
    });
  }

  void _showError(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  void dispose() {
    _roomIdController.dispose();
    _remoteUserIdController.dispose();
    _endCall();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('WebRTC Test')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status display
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _getStatusColor(),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Call Status: $_callStatus',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 24),

            // Room ID input
            TextField(
              controller: _roomIdController,
              decoration: const InputDecoration(
                labelText: 'Room ID',
                border: OutlineInputBorder(),
              ),
              enabled: _callStatus == 'idle',
            ),
            const SizedBox(height: 16),

            // Remote User ID input
            TextField(
              controller: _remoteUserIdController,
              decoration: const InputDecoration(
                labelText: 'Remote User ID',
                border: OutlineInputBorder(),
              ),
              enabled: _callStatus == 'idle',
            ),
            const SizedBox(height: 16),

            // Initiator toggle
            SwitchListTile(
              title: const Text('Initiator'),
              subtitle: Text(
                _isInitiator
                    ? 'You will create the offer'
                    : 'You will receive the offer',
              ),
              value: _isInitiator,
              onChanged:
                  _callStatus == 'idle' ? (value) => _toggleInitiator() : null,
            ),
            const SizedBox(height: 24),

            // Call control buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (_callStatus == 'idle')
                  ElevatedButton(
                    onPressed: _startCall,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    child: const Text('Start Call'),
                  )
                else
                  ElevatedButton(
                    onPressed: _endCall,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 16,
                      ),
                    ),
                    child: const Text('End Call'),
                  ),
              ],
            ),
            const SizedBox(height: 24),

            // Audio controls (only visible during call)
            if (_callStatus != 'idle')
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  // Mute button
                  Column(
                    children: [
                      IconButton(
                        icon: Icon(_isMuted ? Icons.mic_off : Icons.mic),
                        onPressed: _toggleMute,
                        color: _isMuted ? Colors.red : Colors.blue,
                        iconSize: 36,
                      ),
                      Text(_isMuted ? 'Unmute' : 'Mute'),
                    ],
                  ),

                  // Speaker button
                  Column(
                    children: [
                      IconButton(
                        icon: Icon(
                          _isSpeakerOn ? Icons.volume_up : Icons.volume_down,
                        ),
                        onPressed: _toggleSpeaker,
                        color: _isSpeakerOn ? Colors.green : Colors.blue,
                        iconSize: 36,
                      ),
                      Text(_isSpeakerOn ? 'Speaker' : 'Earpiece'),
                    ],
                  ),
                ],
              ),

            const SizedBox(height: 24),

            // Debug information
            const Divider(),
            const Text(
              'Debug Information',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'WebRTC Service: Initialized',
            ),
            const Text(
              'Audio Device Manager: Available',
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (_callStatus) {
      case 'idle':
        return Colors.grey;
      case 'connecting':
        return Colors.orange;
      case 'connected':
        return Colors.green;
      case 'disconnected':
      case 'error':
        return Colors.red;
      default:
        return Colors.blue;
    }
  }
}

/// Entry point for testing WebRTC functionality
void main() {
  runApp(const MaterialApp(home: WebRTCTestScreen()));
}
