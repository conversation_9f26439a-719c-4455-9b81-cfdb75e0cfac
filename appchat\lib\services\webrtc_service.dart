import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart' show debugPrint;
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import '../config/webrtc_config.dart';
import 'audio_device_manager.dart';

/// Service that handles WebRTC functionality for audio calls.
///
/// This class provides methods for establishing, managing, and ending
/// WebRTC connections for audio calls between users.
class WebRTCService {
  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseDatabase _database = FirebaseDatabase.instance;

  // WebRTC related variables
  RTCPeerConnection? _peerConnection;
  MediaStream? _localStream;
  final _localRenderer = RTCVideoRenderer();
  final _remoteRenderer = RTCVideoRenderer();

  // Call state
  String? _currentCallId;
  String? _remoteUserId;
  bool _isInitiator = false;
  bool _isCallActive = false;
  int _connectionAttempts = 0;
  String _iceConnectionState = 'new';

  // Stream controllers for state changes
  final _callStateController = StreamController<String>.broadcast();

  /// Stream of call state changes
  Stream<String> get callStateStream => _callStateController.stream;

  /// Current call ID
  String? get currentCallId => _currentCallId;

  /// Remote user ID
  String? get remoteUserId => _remoteUserId;

  /// Whether the call is active
  bool get isCallActive => _isCallActive;

  /// Constructor
  WebRTCService() {
    _initialize();
  }

  /// Initialize the WebRTC service
  Future<void> _initialize() async {
    await _localRenderer.initialize();
    await _remoteRenderer.initialize();
    developer.log('WebRTC service initialized');
  }

  /// Initialize a call
  ///
  /// Sets up the WebRTC connection and signaling for a call.
  /// [callId] is the unique identifier for the call.
  /// [remoteUserId] is the ID of the user being called.
  /// [isInitiator] indicates whether this user is initiating the call.
  Future<void> initializeCall(
    String callId,
    String remoteUserId,
    bool isInitiator,
  ) async {
    if (_isCallActive) {
      await endCall();
    }

    _currentCallId = callId;
    _remoteUserId = remoteUserId;
    _isInitiator = isInitiator;
    _isCallActive = true;

    _callStateController.add('connecting');

    try {
      // Create peer connection
      _peerConnection = await _createPeerConnection();

      // Set up local media stream
      _localStream = await _getUserMedia();

      // Add tracks to peer connection
      _localStream!.getTracks().forEach((track) {
        _peerConnection!.addTrack(track, _localStream!);
      });

      // Set up signaling
      await _setupSignaling();

      // Create offer if initiator
      if (_isInitiator) {
        await _createOffer();
      }

      developer.log(
        'Call initialized: $callId with remote user: $remoteUserId',
      );
    } catch (e) {
      developer.log('Error initializing call: $e');
      _callStateController.add('error');
      await endCall();
    }
  }

  /// Create a WebRTC peer connection
  Future<RTCPeerConnection> _createPeerConnection() async {
    _connectionAttempts++;

    // Use the configuration from WebRTCConfig
    final configuration = {'iceServers': WebRTCConfig.iceServers};

    debugPrint(
      'Creating peer connection with ${WebRTCConfig.iceServers.length} ICE servers',
    );
    for (var server in WebRTCConfig.iceServers) {
      debugPrint('ICE Server: ${server['urls']}');
    }

    // Create a new RTCPeerConnection instance with proper configuration
    final pc = await createPeerConnection(
      configuration,
      WebRTCConfig.webRTCOptions,
    );

    // Set up event handlers
    pc.onIceCandidate = (candidate) {
      debugPrint('Local ICE candidate: ${candidate.candidate}');
      _sendIceCandidate(candidate);
    };

    pc.onIceConnectionState = (state) {
      _iceConnectionState = state.toString();
      debugPrint('ICE connection state changed: $state');

      // Update connection state in database for debugging
      _updateConnectionState(state.toString());

      if (state == RTCIceConnectionState.RTCIceConnectionStateConnected) {
        debugPrint('WebRTC connection established successfully!');
        _callStateController.add('connected');
      } else if (state == RTCIceConnectionState.RTCIceConnectionStateFailed) {
        debugPrint('WebRTC connection failed!');
        _callStateController.add('disconnected');
      } else if (state ==
          RTCIceConnectionState.RTCIceConnectionStateDisconnected) {
        debugPrint('WebRTC connection disconnected!');
        _callStateController.add('disconnected');
      }
    };

    pc.onTrack = (event) {
      debugPrint('Remote track received: ${event.track.kind}');
      if (event.streams.isNotEmpty) {
        _remoteRenderer.srcObject = event.streams[0];
        _callStateController.add('connected');
      }
    };

    pc.onIceGatheringState = (state) {
      debugPrint('ICE gathering state: $state');
    };

    pc.onSignalingState = (state) {
      debugPrint('Signaling state: $state');
    };

    return pc;
  }

  /// Update connection state in database for debugging
  void _updateConnectionState(String state) {
    if (_currentCallId == null) return;

    try {
      _database
          .ref()
          .child('calls')
          .child('active')
          .child(_currentCallId!)
          .update({
            'webrtcState': state,
            'connectionAttempts': _connectionAttempts,
            'lastUpdated': ServerValue.timestamp,
          });
    } catch (e) {
      debugPrint('Error updating connection state: $e');
    }
  }

  /// Get user media (audio only)
  Future<MediaStream> _getUserMedia() async {
    try {
      debugPrint('Getting user media with audio constraints');
      final mediaConstraints = WebRTCConfig.audioConstraints;
      debugPrint('Media constraints: $mediaConstraints');

      final stream = await navigator.mediaDevices.getUserMedia(
        mediaConstraints,
      );
      debugPrint('Got media stream with ${stream.getTracks().length} tracks');

      stream.getTracks().forEach((track) {
        debugPrint(
          'Track: ${track.kind}, enabled: ${track.enabled}, id: ${track.id}',
        );
      });

      _localRenderer.srcObject = stream;
      debugPrint('Set local renderer source object');

      // Update database with media info for debugging
      if (_currentCallId != null) {
        await _database
            .ref()
            .child('calls')
            .child('active')
            .child(_currentCallId!)
            .update({
              'localMediaObtained': true,
              'localMediaObtainedAt': ServerValue.timestamp,
              'localMediaTracks': stream.getTracks().length,
            });
      }

      return stream;
    } catch (e) {
      debugPrint('ERROR GETTING USER MEDIA: $e');

      // Update database with error
      if (_currentCallId != null) {
        await _database
            .ref()
            .child('calls')
            .child('active')
            .child(_currentCallId!)
            .update({
              'mediaError': e.toString(),
              'mediaErrorAt': ServerValue.timestamp,
            });
      }

      rethrow;
    }
  }

  /// Set up signaling for WebRTC
  Future<void> _setupSignaling() async {
    if (_currentCallId == null) return;

    try {
      debugPrint('Setting up WebRTC signaling for call: $_currentCallId');

      // First, ensure the room document exists in Firestore
      final roomRef = _firestore.collection('rooms').doc(_currentCallId);
      final roomDoc = await roomRef.get();

      // If room doesn't exist, create it
      if (!roomDoc.exists) {
        debugPrint(
          'Creating Firestore room document for call: $_currentCallId',
        );
        await roomRef.set({
          'createdAt': FieldValue.serverTimestamp(),
          'createdBy': _auth.currentUser?.uid ?? 'unknown',
          'isActive': true,
          'description': 'WebRTC call room',
          'callId': _currentCallId,
          'participants': [
            _auth.currentUser?.uid ?? 'unknown',
            _remoteUserId ?? 'unknown',
          ],
        });
      }

      // Also update the Realtime Database call record with Firestore room info
      await _database
          .ref()
          .child('calls')
          .child('active')
          .child(_currentCallId!)
          .update({
            'firestoreRoomId': _currentCallId,
            'webrtcSetupTime': ServerValue.timestamp,
          });

      // Use Firestore for signaling
      final signalingRef = roomRef.collection('signaling');

      // Listen for remote signaling messages (offers and answers)
      signalingRef.snapshots().listen((snapshot) {
        for (var change in snapshot.docChanges) {
          if (change.type == DocumentChangeType.added) {
            final data = change.doc.data();
            if (data != null && data['senderId'] != _auth.currentUser?.uid) {
              debugPrint(
                'Received signaling message: ${data['type']} from ${data['senderId']}',
              );
              _handleSignalingMessage(data);
            }
          }
        }
      });

      // Listen for ICE candidates in a separate collection
      final candidatesRef = roomRef.collection('candidates');

      // Listen for remote ICE candidates
      candidatesRef.snapshots().listen((snapshot) {
        for (var change in snapshot.docChanges) {
          if (change.type == DocumentChangeType.added) {
            final data = change.doc.data();
            if (data != null && data['senderId'] != _auth.currentUser?.uid) {
              debugPrint('Received ICE candidate from ${data['senderId']}');
              _handleIceCandidate(data);
            }
          }
        }
      });

      debugPrint('Signaling setup complete for call: $_currentCallId');
    } catch (e) {
      debugPrint('Error setting up signaling: $e');
      _callStateController.add('error');
    }
  }

  /// Handle incoming ICE candidate
  Future<void> _handleIceCandidate(Map<String, dynamic> data) async {
    if (_peerConnection == null) return;

    try {
      final candidate = RTCIceCandidate(
        data['candidate'],
        data['sdpMid'],
        data['sdpMLineIndex'],
      );

      debugPrint('Adding remote ICE candidate: ${data['candidate']}');
      await _peerConnection!.addCandidate(candidate);
      debugPrint('Remote ICE candidate added successfully');

      // Update database with candidate info for debugging
      if (_currentCallId != null) {
        await _database
            .ref()
            .child('calls')
            .child('active')
            .child(_currentCallId!)
            .update({
              'lastCandidateReceived': ServerValue.timestamp,
              'candidatesReceived': ServerValue.increment(1),
            });
      }
    } catch (e) {
      debugPrint('Error handling ICE candidate: $e');
    }
  }

  /// Handle incoming signaling messages
  void _handleSignalingMessage(Map<String, dynamic> message) async {
    final type = message['type'];

    switch (type) {
      case 'offer':
        if (!_isInitiator && _peerConnection != null) {
          debugPrint('Received offer from remote peer');
          debugPrint('SDP offer: ${message['sdp']}');

          try {
            final description = RTCSessionDescription(
              message['sdp'],
              message['type'],
            );

            debugPrint('Setting remote description (offer)');
            await _peerConnection!.setRemoteDescription(description);

            debugPrint('Creating answer');
            await _createAnswer();

            // Update database with signaling info for debugging
            if (_currentCallId != null) {
              await _database
                  .ref()
                  .child('calls')
                  .child('active')
                  .child(_currentCallId!)
                  .update({
                    'offerReceived': true,
                    'offerReceivedAt': ServerValue.timestamp,
                  });
            }
          } catch (e) {
            debugPrint('Error processing offer: $e');
          }
        }
        break;

      case 'answer':
        if (_isInitiator && _peerConnection != null) {
          debugPrint('Received answer from remote peer');
          debugPrint('SDP answer: ${message['sdp']}');

          try {
            final description = RTCSessionDescription(
              message['sdp'],
              message['type'],
            );

            debugPrint('Setting remote description (answer)');
            await _peerConnection!.setRemoteDescription(description);

            // Update database with signaling info for debugging
            if (_currentCallId != null) {
              await _database
                  .ref()
                  .child('calls')
                  .child('active')
                  .child(_currentCallId!)
                  .update({
                    'answerReceived': true,
                    'answerReceivedAt': ServerValue.timestamp,
                  });
            }
          } catch (e) {
            debugPrint('Error processing answer: $e');
          }
        }
        break;
    }
  }

  /// Create and send an offer
  Future<void> _createOffer() async {
    if (_peerConnection == null || _currentCallId == null) return;

    try {
      debugPrint('Creating offer for call: $_currentCallId');
      final offer = await _peerConnection!.createOffer();

      debugPrint('Setting local description (offer)');
      await _peerConnection!.setLocalDescription(offer);

      debugPrint('Sending offer to remote peer: $_remoteUserId');
      // Send offer to remote peer
      await _firestore
          .collection('rooms')
          .doc(_currentCallId)
          .collection('signaling')
          .add({
            'type': offer.type,
            'sdp': offer.sdp,
            'senderId': _auth.currentUser?.uid ?? 'unknown',
            'timestamp': FieldValue.serverTimestamp(),
            'targetUserId': _remoteUserId,
          });

      // Update database with signaling info for debugging
      await _database
          .ref()
          .child('calls')
          .child('active')
          .child(_currentCallId!)
          .update({
            'offerCreated': true,
            'offerCreatedAt': ServerValue.timestamp,
          });

      debugPrint('Offer created and sent successfully');
    } catch (e) {
      debugPrint('Error creating offer: $e');
      _callStateController.add('error');
    }
  }

  /// Create and send an answer
  Future<void> _createAnswer() async {
    if (_peerConnection == null || _currentCallId == null) return;

    try {
      debugPrint('Creating answer for call: $_currentCallId');
      final answer = await _peerConnection!.createAnswer();

      debugPrint('Setting local description (answer)');
      await _peerConnection!.setLocalDescription(answer);

      debugPrint('Sending answer to remote peer: $_remoteUserId');
      // Send answer to remote peer
      await _firestore
          .collection('rooms')
          .doc(_currentCallId)
          .collection('signaling')
          .add({
            'type': answer.type,
            'sdp': answer.sdp,
            'senderId': _auth.currentUser?.uid ?? 'unknown',
            'timestamp': FieldValue.serverTimestamp(),
            'targetUserId': _remoteUserId,
          });

      // Update database with signaling info for debugging
      await _database
          .ref()
          .child('calls')
          .child('active')
          .child(_currentCallId!)
          .update({
            'answerCreated': true,
            'answerCreatedAt': ServerValue.timestamp,
          });

      debugPrint('Answer created and sent successfully');
    } catch (e) {
      debugPrint('Error creating answer: $e');
      _callStateController.add('error');
    }
  }

  /// Send ICE candidate to remote peer
  Future<void> _sendIceCandidate(RTCIceCandidate candidate) async {
    if (_currentCallId == null) return;

    try {
      debugPrint(
        'Sending ICE candidate to remote peer: ${candidate.candidate}',
      );

      // Store ICE candidates in a separate collection for better performance
      await _firestore
          .collection('rooms')
          .doc(_currentCallId)
          .collection('candidates')
          .add({
            'candidate': candidate.candidate,
            'sdpMid': candidate.sdpMid,
            'sdpMLineIndex': candidate.sdpMLineIndex,
            'senderId': _auth.currentUser?.uid ?? 'unknown',
            'timestamp': FieldValue.serverTimestamp(),
            'targetUserId': _remoteUserId,
          });

      // Update database with candidate info for debugging
      await _database
          .ref()
          .child('calls')
          .child('active')
          .child(_currentCallId!)
          .update({
            'lastCandidateSent': ServerValue.timestamp,
            'candidatesSent': ServerValue.increment(1),
          });

      debugPrint('ICE candidate sent successfully');
    } catch (e) {
      debugPrint('Error sending ICE candidate: $e');
      // Don't set error state for ICE candidate failures as they can be transient
    }
  }

  /// Set microphone muted state
  Future<void> setMicrophoneMuted(bool muted) async {
    if (_localStream != null) {
      final audioTracks = _localStream!.getAudioTracks();
      for (var track in audioTracks) {
        track.enabled = !muted;
      }
      developer.log('Microphone ${muted ? 'muted' : 'unmuted'}');
    }
  }

  /// Set speaker enabled state
  Future<void> setSpeakerEnabled(bool enabled) async {
    try {
      // Use the AudioDeviceManager to handle platform-specific speaker control
      final audioDeviceManager = AudioDeviceManager();
      await audioDeviceManager.setSpeakerEnabled(enabled);
      developer.log('Speaker ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      developer.log('Error setting speaker state: $e');
    }
  }

  /// End the current call
  Future<void> endCall() async {
    _isCallActive = false;
    _callStateController.add('idle');

    // Close peer connection
    if (_peerConnection != null) {
      await _peerConnection!.close();
      _peerConnection = null;
    }

    // Close local stream
    if (_localStream != null) {
      _localStream!.getTracks().forEach((track) => track.stop());
      _localStream = null;
    }

    // Reset renderers
    _localRenderer.srcObject = null;
    _remoteRenderer.srcObject = null;

    // Reset call state
    _currentCallId = null;
    _remoteUserId = null;
    _isInitiator = false;

    developer.log('Call ended');
  }

  /// Dispose resources
  void dispose() {
    endCall();
    _localRenderer.dispose();
    _remoteRenderer.dispose();
    _callStateController.close();
    developer.log('WebRTC service disposed');
  }
}
