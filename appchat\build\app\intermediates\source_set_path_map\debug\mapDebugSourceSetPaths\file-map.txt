com.hamode.appchat.app-jetified-play-services-base-18.5.0-0 C:\Users\<USER>\.gradle\caches\8.9\transforms\04f854c86687d4911c82dc9952d1fb33\transformed\jetified-play-services-base-18.5.0\res
com.hamode.appchat.app-jetified-tracing-1.2.0-1 C:\Users\<USER>\.gradle\caches\8.9\transforms\0845a10208de5f6bf1ba669964f54555\transformed\jetified-tracing-1.2.0\res
com.hamode.appchat.app-appcompat-1.1.0-2 C:\Users\<USER>\.gradle\caches\8.9\transforms\0a2895bad47a5ad7be3689da4d9dc4ce\transformed\appcompat-1.1.0\res
com.hamode.appchat.app-jetified-firebase-messaging-24.1.1-3 C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\res
com.hamode.appchat.app-jetified-lifecycle-process-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\res
com.hamode.appchat.app-jetified-savedstate-1.2.1-5 C:\Users\<USER>\.gradle\caches\8.9\transforms\10e4d1e9843fdd914e767b7a564d561f\transformed\jetified-savedstate-1.2.1\res
com.hamode.appchat.app-localbroadcastmanager-1.1.0-6 C:\Users\<USER>\.gradle\caches\8.9\transforms\1564a3c50ad5622247eb8bcb9b3d07c9\transformed\localbroadcastmanager-1.1.0\res
com.hamode.appchat.app-jetified-window-java-1.2.0-7 C:\Users\<USER>\.gradle\caches\8.9\transforms\1d50269b9da106d44b67d80ef482cdef\transformed\jetified-window-java-1.2.0\res
com.hamode.appchat.app-jetified-lifecycle-livedata-core-ktx-2.7.0-8 C:\Users\<USER>\.gradle\caches\8.9\transforms\1eb7180f04d17ac00c79e325906dadb2\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.hamode.appchat.app-slidingpanelayout-1.2.0-9 C:\Users\<USER>\.gradle\caches\8.9\transforms\212d2490ba3580700f690023245bd33a\transformed\slidingpanelayout-1.2.0\res
com.hamode.appchat.app-lifecycle-viewmodel-2.7.0-10 C:\Users\<USER>\.gradle\caches\8.9\transforms\34eb00f8a80711cfd79dd0d59eb42f9e\transformed\lifecycle-viewmodel-2.7.0\res
com.hamode.appchat.app-jetified-core-common-2.0.3-11 C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\res
com.hamode.appchat.app-lifecycle-livedata-core-2.7.0-12 C:\Users\<USER>\.gradle\caches\8.9\transforms\3a5e246f9b886670304a12dcf99933cb\transformed\lifecycle-livedata-core-2.7.0\res
com.hamode.appchat.app-jetified-startup-runtime-1.1.1-13 C:\Users\<USER>\.gradle\caches\8.9\transforms\3a6815842f40a96182f76dd0a078a260\transformed\jetified-startup-runtime-1.1.1\res
com.hamode.appchat.app-jetified-play-services-auth-21.0.0-14 C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\res
com.hamode.appchat.app-transition-1.4.1-15 C:\Users\<USER>\.gradle\caches\8.9\transforms\4234453af03c8dd3678be743c89a5111\transformed\transition-1.4.1\res
com.hamode.appchat.app-jetified-appcompat-resources-1.1.0-16 C:\Users\<USER>\.gradle\caches\8.9\transforms\44deec49b42db3e4802e194c18814659\transformed\jetified-appcompat-resources-1.1.0\res
com.hamode.appchat.app-lifecycle-runtime-2.7.0-17 C:\Users\<USER>\.gradle\caches\8.9\transforms\46f0063f0b141a80d04175fe19bf8716\transformed\lifecycle-runtime-2.7.0\res
com.hamode.appchat.app-jetified-ads-adservices-java-1.1.0-beta11-18 C:\Users\<USER>\.gradle\caches\8.9\transforms\508e947379c8aa51742d2941b1b5d5df\transformed\jetified-ads-adservices-java-1.1.0-beta11\res
com.hamode.appchat.app-jetified-activity-1.8.1-19 C:\Users\<USER>\.gradle\caches\8.9\transforms\5aa4ddee228735d62213dec8af0f141d\transformed\jetified-activity-1.8.1\res
com.hamode.appchat.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-20 C:\Users\<USER>\.gradle\caches\8.9\transforms\5c6d5121e187b3db0721bead545b3fe6\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.hamode.appchat.app-preference-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.9\transforms\5cbc467f2278e032b60487f8f70af125\transformed\preference-1.2.1\res
com.hamode.appchat.app-coordinatorlayout-1.0.0-22 C:\Users\<USER>\.gradle\caches\8.9\transforms\5e7bf53de382ca438baad6d41cafcd0d\transformed\coordinatorlayout-1.0.0\res
com.hamode.appchat.app-jetified-core-1.0.0-23 C:\Users\<USER>\.gradle\caches\8.9\transforms\6315082e0fab4443afc03674c4cb1870\transformed\jetified-core-1.0.0\res
com.hamode.appchat.app-jetified-firebase-common-21.0.0-24 C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\res
com.hamode.appchat.app-browser-1.4.0-25 C:\Users\<USER>\.gradle\caches\8.9\transforms\70d9edd68c4ac01d9150f2de0a6ef51d\transformed\browser-1.4.0\res
com.hamode.appchat.app-jetified-play-services-basement-18.5.0-26 C:\Users\<USER>\.gradle\caches\8.9\transforms\82766f43c900f418c4a97a171253098c\transformed\jetified-play-services-basement-18.5.0\res
com.hamode.appchat.app-core-runtime-2.2.0-27 C:\Users\<USER>\.gradle\caches\8.9\transforms\96553c43ef084137cd134c911d5c8233\transformed\core-runtime-2.2.0\res
com.hamode.appchat.app-jetified-core-ktx-1.13.1-28 C:\Users\<USER>\.gradle\caches\8.9\transforms\99aa92b4b0fe7bae208a9e1cc1147c38\transformed\jetified-core-ktx-1.13.1\res
com.hamode.appchat.app-jetified-ads-adservices-1.1.0-beta11-29 C:\Users\<USER>\.gradle\caches\8.9\transforms\9aeb0abcba29958216af2915c1c4fcb2\transformed\jetified-ads-adservices-1.1.0-beta11\res
com.hamode.appchat.app-core-1.13.1-30 C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\res
com.hamode.appchat.app-jetified-datastore-release-31 C:\Users\<USER>\.gradle\caches\8.9\transforms\a5e0f94ba9470edafc76bf4f9d4a5016\transformed\jetified-datastore-release\res
com.hamode.appchat.app-jetified-lifecycle-runtime-ktx-2.7.0-32 C:\Users\<USER>\.gradle\caches\8.9\transforms\aa36494dd2b56d41430bb657ad123fd9\transformed\jetified-lifecycle-runtime-ktx-2.7.0\res
com.hamode.appchat.app-jetified-credentials-1.2.0-rc01-33 C:\Users\<USER>\.gradle\caches\8.9\transforms\ab37722a56b48a618e91dc4d4ff760f9\transformed\jetified-credentials-1.2.0-rc01\res
com.hamode.appchat.app-jetified-credentials-play-services-auth-1.2.0-rc01-34 C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.hamode.appchat.app-recyclerview-1.0.0-35 C:\Users\<USER>\.gradle\caches\8.9\transforms\af4ee8b531c489f37c57d7ba50242bad\transformed\recyclerview-1.0.0\res
com.hamode.appchat.app-jetified-lifecycle-viewmodel-ktx-2.7.0-36 C:\Users\<USER>\.gradle\caches\8.9\transforms\b16a49b0a575a661b732d2354c14e66e\transformed\jetified-lifecycle-viewmodel-ktx-2.7.0\res
com.hamode.appchat.app-jetified-activity-ktx-1.8.1-37 C:\Users\<USER>\.gradle\caches\8.9\transforms\b2d164112074b0448204847df6a8e2fc\transformed\jetified-activity-ktx-1.8.1\res
com.hamode.appchat.app-jetified-window-1.2.0-38 C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\res
com.hamode.appchat.app-jetified-fragment-ktx-1.7.1-39 C:\Users\<USER>\.gradle\caches\8.9\transforms\de5a56048f5b0921e6cf47fda5e6b9a8\transformed\jetified-fragment-ktx-1.7.1\res
com.hamode.appchat.app-jetified-savedstate-ktx-1.2.1-40 C:\Users\<USER>\.gradle\caches\8.9\transforms\e55c72d3cdd9b5e84f81ade9b4ab0aba\transformed\jetified-savedstate-ktx-1.2.1\res
com.hamode.appchat.app-jetified-datastore-core-release-41 C:\Users\<USER>\.gradle\caches\8.9\transforms\ef4dde4921424728053ce264501ca8e5\transformed\jetified-datastore-core-release\res
com.hamode.appchat.app-fragment-1.7.1-42 C:\Users\<USER>\.gradle\caches\8.9\transforms\f08b39e59dae4f15e2301f875d7ebef3\transformed\fragment-1.7.1\res
com.hamode.appchat.app-jetified-profileinstaller-1.3.1-43 C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\res
com.hamode.appchat.app-jetified-datastore-preferences-release-44 C:\Users\<USER>\.gradle\caches\8.9\transforms\f44d8934906c7b7a3ccd8d47abc12d09\transformed\jetified-datastore-preferences-release\res
com.hamode.appchat.app-jetified-annotation-experimental-1.4.0-45 C:\Users\<USER>\.gradle\caches\8.9\transforms\f623eee13ac12144a6e4f078e29d717c\transformed\jetified-annotation-experimental-1.4.0\res
com.hamode.appchat.app-debug-46 D:\ChatApp\appchat\android\app\src\debug\res
com.hamode.appchat.app-main-47 D:\ChatApp\appchat\android\app\src\main\res
com.hamode.appchat.app-pngs-48 D:\ChatApp\appchat\build\app\generated\res\pngs\debug
com.hamode.appchat.app-res-49 D:\ChatApp\appchat\build\app\generated\res\processDebugGoogleServices
com.hamode.appchat.app-resValues-50 D:\ChatApp\appchat\build\app\generated\res\resValues\debug
com.hamode.appchat.app-packageDebugResources-51 D:\ChatApp\appchat\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.hamode.appchat.app-packageDebugResources-52 D:\ChatApp\appchat\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.hamode.appchat.app-debug-53 D:\ChatApp\appchat\build\app\intermediates\merged_res\debug\mergeDebugResources
com.hamode.appchat.app-debug-54 D:\ChatApp\appchat\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-55 D:\ChatApp\appchat\build\connectivity_plus\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-56 D:\ChatApp\appchat\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-57 D:\ChatApp\appchat\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-58 D:\ChatApp\appchat\build\firebase_database\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-59 D:\ChatApp\appchat\build\firebase_messaging\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-60 D:\ChatApp\appchat\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-61 D:\ChatApp\appchat\build\flutter_webrtc\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-62 D:\ChatApp\appchat\build\google_sign_in_android\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-63 D:\ChatApp\appchat\build\path_provider_android\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-64 D:\ChatApp\appchat\build\permission_handler_android\intermediates\packaged_res\debug\packageDebugResources
com.hamode.appchat.app-debug-65 D:\ChatApp\appchat\build\shared_preferences_android\intermediates\packaged_res\debug\packageDebugResources
