1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.hamode.appchat"
4    android:versionCode="2"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:3:5-67
15-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:3:22-64
16    <!-- Microphone permission for audio calls -->
17    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- Notification permissions -->
17-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:5:5-71
17-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:5:22-68
18    <uses-permission android:name="android.permission.VIBRATE" />
18-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:7:5-66
18-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:7:22-63
19    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
19-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:8:5-81
19-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:8:22-78
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:47:5-52:15
28        <intent>
28-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:48:9-51:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:49:13-72
29-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:49:21-70
30
31            <data android:mimeType="text/plain" />
31-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:50:13-50
31-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:50:19-48
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.WAKE_LOCK" />
35-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
35-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
36-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
36-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
37    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Required by older versions of Google Play services to create IID tokens -->
37-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
37-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
38    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
38-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:5-82
38-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:26:22-79
39    <uses-permission
39-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cbbe430497dfcc884a379b31efad869\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:9:5-11:38
40        android:name="android.permission.BLUETOOTH"
40-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cbbe430497dfcc884a379b31efad869\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:10:9-52
41        android:maxSdkVersion="30" />
41-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cbbe430497dfcc884a379b31efad869\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:11:9-35
42    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
42-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cbbe430497dfcc884a379b31efad869\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:12:5-80
42-->[com.github.davidliu:audioswitch:89582c47c9a04c62f90aa5e57251af4800a62c9a] C:\Users\<USER>\.gradle\caches\8.9\transforms\3cbbe430497dfcc884a379b31efad869\transformed\jetified-audioswitch-89582c47c9a04c62f90aa5e57251af4800a62c9a\AndroidManifest.xml:12:22-77
43    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
43-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:5-79
43-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:25:22-76
44    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
44-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:5-88
44-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:26:22-85
45    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
45-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:5-82
45-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:27:22-79
46    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
46-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4cd06e0e0a286661102a0a7e08fcb47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
46-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4cd06e0e0a286661102a0a7e08fcb47\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
47    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:5-110
47-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:26:22-107
48
49    <permission
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
50        android:name="com.hamode.appchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
51        android:protectionLevel="signature" />
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
52
53    <uses-permission android:name="com.hamode.appchat.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
54
55    <application
56        android:name="android.app.Application"
57        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
57-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\9ecd58143158fe7544d1e8fee291fc86\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
58        android:debuggable="true"
59        android:enableOnBackInvokedCallback="true"
60        android:extractNativeLibs="false"
61        android:icon="@mipmap/ic_launcher"
62        android:label="Чат рулетка Хамуди.me" >
63        <activity
64            android:name="com.hamode.appchat.MainActivity"
65            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
66            android:exported="true"
67            android:hardwareAccelerated="true"
68            android:launchMode="singleTop"
69            android:taskAffinity=""
70            android:theme="@style/LaunchTheme"
71            android:windowSoftInputMode="adjustResize" >
72
73            <!--
74                 Specifies an Android theme to apply to this Activity as soon as
75                 the Android process has started. This theme is visible to the user
76                 while the Flutter UI initializes. After that, this theme continues
77                 to determine the Window background behind the Flutter UI.
78            -->
79            <meta-data
80                android:name="io.flutter.embedding.android.NormalTheme"
81                android:resource="@style/NormalTheme" />
82
83            <intent-filter>
84                <action android:name="android.intent.action.MAIN" />
85
86                <category android:name="android.intent.category.LAUNCHER" />
87            </intent-filter>
88        </activity>
89        <!--
90             Don't delete the meta-data below.
91             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
92        -->
93        <meta-data
94            android:name="flutterEmbedding"
95            android:value="2" />
96
97        <service
97-->[:cloud_firestore] D:\ChatApp\appchat\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
98            android:name="com.google.firebase.components.ComponentDiscoveryService"
98-->[:cloud_firestore] D:\ChatApp\appchat\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
99            android:directBootAware="true"
99-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
100            android:exported="false" >
100-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:68:13-37
101            <meta-data
101-->[:cloud_firestore] D:\ChatApp\appchat\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
102-->[:cloud_firestore] D:\ChatApp\appchat\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:cloud_firestore] D:\ChatApp\appchat\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
104            <meta-data
104-->[:firebase_auth] D:\ChatApp\appchat\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
105                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
105-->[:firebase_auth] D:\ChatApp\appchat\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[:firebase_auth] D:\ChatApp\appchat\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
107            <meta-data
107-->[:firebase_database] D:\ChatApp\appchat\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
108                android:name="com.google.firebase.components:io.flutter.plugins.firebase.database.FlutterFirebaseAppRegistrar"
108-->[:firebase_database] D:\ChatApp\appchat\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-127
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[:firebase_database] D:\ChatApp\appchat\build\firebase_database\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
110            <meta-data
110-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
111                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
111-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
113            <meta-data
113-->[:firebase_storage] D:\ChatApp\appchat\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
114                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
114-->[:firebase_storage] D:\ChatApp\appchat\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[:firebase_storage] D:\ChatApp\appchat\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
116            <meta-data
116-->[:firebase_core] D:\ChatApp\appchat\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
117                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
117-->[:firebase_core] D:\ChatApp\appchat\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[:firebase_core] D:\ChatApp\appchat\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
119            <meta-data
119-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:69:13-71:85
120                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
120-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:70:17-109
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:71:17-82
122            <meta-data
122-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
123                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
123-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:30:17-120
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:31:17-82
125            <meta-data
125-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
126                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
126-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:33:17-109
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\1ca8a8c8beab3813e72c486e10c3ed78\transformed\jetified-firebase-database-21.0.0\AndroidManifest.xml:34:17-82
128            <meta-data
128-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:17:13-19:85
129                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
129-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:18:17-122
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:19:17-82
131            <meta-data
131-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:20:13-22:85
132                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
132-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:21:17-111
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-firestore:25.1.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\b106793888b51a56cfa0df24736514f2\transformed\jetified-firebase-firestore-25.1.3\AndroidManifest.xml:22:17-82
134            <meta-data
134-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:57:13-59:85
135                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
135-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:58:17-122
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:59:17-82
137            <meta-data
137-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:60:13-62:85
138                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
138-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:61:17-119
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:62:17-82
140            <meta-data
140-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
141                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
141-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
143            <meta-data
143-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
144                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
144-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5d08ed88861b1d776d9c8ba650d13263\transformed\jetified-firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
146            <meta-data
146-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:33:13-35:85
147                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
147-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:34:17-139
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.android.gms:play-services-measurement-api:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\42bb7346cd88dba4c738c7dbaf95dee3\transformed\jetified-play-services-measurement-api-22.4.0\AndroidManifest.xml:35:17-82
149            <meta-data
149-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
150                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
150-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
152            <meta-data
152-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
153                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
153-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\fe56f971ed8af6270be1c2264a9308c0\transformed\jetified-firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
155            <meta-data
155-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
156                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
156-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
157                android:value="com.google.firebase.components.ComponentRegistrar" />
157-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
158            <meta-data
158-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
159                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
159-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
160                android:value="com.google.firebase.components.ComponentRegistrar" />
160-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\16e3bf994c62d28aee9102b02a0c490f\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
161            <meta-data
161-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59ea1c9237739ff6825a39ec656dd215\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
162                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
162-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59ea1c9237739ff6825a39ec656dd215\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
163                android:value="com.google.firebase.components.ComponentRegistrar" />
163-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\59ea1c9237739ff6825a39ec656dd215\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
164            <meta-data
164-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
165                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
165-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
166                android:value="com.google.firebase.components.ComponentRegistrar" />
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
167            <meta-data
167-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2f66c3d8b74f9c3753d4189d1fa9ddc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:25:13-27:85
168                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
168-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2f66c3d8b74f9c3753d4189d1fa9ddc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:26:17-115
169                android:value="com.google.firebase.components.ComponentRegistrar" />
169-->[com.google.firebase:firebase-datatransport:18.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\a2f66c3d8b74f9c3753d4189d1fa9ddc\transformed\jetified-firebase-datatransport-18.2.0\AndroidManifest.xml:27:17-82
170        </service>
171        <service
171-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
172            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
172-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
173            android:exported="false"
173-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
174            android:permission="android.permission.BIND_JOB_SERVICE" />
174-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
175        <service
175-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
176            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
176-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
177            android:exported="false" >
177-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
178            <intent-filter>
178-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
179                <action android:name="com.google.firebase.MESSAGING_EVENT" />
179-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
179-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
180            </intent-filter>
181        </service>
182
183        <receiver
183-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
184            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
184-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
185            android:exported="true"
185-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
186            android:permission="com.google.android.c2dm.permission.SEND" >
186-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
187            <intent-filter>
187-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
188                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
188-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
188-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
189            </intent-filter>
190        </receiver>
191
192        <provider
192-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
193            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
193-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
194            android:authorities="com.hamode.appchat.flutterfirebasemessaginginitprovider"
194-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
195            android:exported="false"
195-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
196            android:initOrder="99" />
196-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
197
198        <activity
198-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:29:9-46:20
199            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
199-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:30:13-80
200            android:excludeFromRecents="true"
200-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:31:13-46
201            android:exported="true"
201-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:32:13-36
202            android:launchMode="singleTask"
202-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:33:13-44
203            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
203-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:34:13-72
204            <intent-filter>
204-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:35:13-45:29
205                <action android:name="android.intent.action.VIEW" />
205-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
205-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
206
207                <category android:name="android.intent.category.DEFAULT" />
207-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
207-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
208                <category android:name="android.intent.category.BROWSABLE" />
208-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
208-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
209
210                <data
210-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:50:13-50
211                    android:host="firebase.auth"
212                    android:path="/"
213                    android:scheme="genericidp" />
214            </intent-filter>
215        </activity>
216        <activity
216-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:47:9-64:20
217            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
217-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:48:13-79
218            android:excludeFromRecents="true"
218-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:49:13-46
219            android:exported="true"
219-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:50:13-36
220            android:launchMode="singleTask"
220-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:51:13-44
221            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
221-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:52:13-72
222            <intent-filter>
222-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:53:13-63:29
223                <action android:name="android.intent.action.VIEW" />
223-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:17-69
223-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:36:25-66
224
225                <category android:name="android.intent.category.DEFAULT" />
225-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:17-76
225-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:38:27-73
226                <category android:name="android.intent.category.BROWSABLE" />
226-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:17-78
226-->[com.google.firebase:firebase-auth:23.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4f29a84680d5d738b762fde46fbeb49\transformed\jetified-firebase-auth-23.2.0\AndroidManifest.xml:39:27-75
227
228                <data
228-->D:\ChatApp\appchat\android\app\src\main\AndroidManifest.xml:50:13-50
229                    android:host="firebase.auth"
230                    android:path="/"
231                    android:scheme="recaptcha" />
232            </intent-filter>
233        </activity>
234
235        <receiver
235-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:29:9-40:20
236            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
236-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:30:13-78
237            android:exported="true"
237-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:31:13-36
238            android:permission="com.google.android.c2dm.permission.SEND" >
238-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:32:13-73
239            <intent-filter>
239-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
240                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
240-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
240-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
241            </intent-filter>
242
243            <meta-data
243-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:37:13-39:40
244                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
244-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:38:17-92
245                android:value="true" />
245-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:39:17-37
246        </receiver>
247        <!--
248             FirebaseMessagingService performs security checks at runtime,
249             but set to not exported to explicitly avoid allowing another app to call it.
250        -->
251        <service
251-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:46:9-53:19
252            android:name="com.google.firebase.messaging.FirebaseMessagingService"
252-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:47:13-82
253            android:directBootAware="true"
253-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:48:13-43
254            android:exported="false" >
254-->[com.google.firebase:firebase-messaging:24.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\0cdbb293b2f4b225ee91f9c052d59846\transformed\jetified-firebase-messaging-24.1.1\AndroidManifest.xml:49:13-37
255            <intent-filter android:priority="-500" >
255-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
256                <action android:name="com.google.firebase.MESSAGING_EVENT" />
256-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
256-->[:firebase_messaging] D:\ChatApp\appchat\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
257            </intent-filter>
258        </service>
259        <service
259-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
260            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
260-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
261            android:enabled="true"
261-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
262            android:exported="false" >
262-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
263            <meta-data
263-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
264                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
264-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
265                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
265-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
266        </service>
267
268        <activity
268-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
269            android:name="androidx.credentials.playservices.HiddenActivity"
269-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
270            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
270-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
271            android:enabled="true"
271-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
272            android:exported="false"
272-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
273            android:fitsSystemWindows="true"
273-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
274            android:theme="@style/Theme.Hidden" >
274-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.9\transforms\ac6c3e60e79795e6824fd4812a22a815\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
275        </activity>
276
277        <provider
277-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
278            android:name="com.google.firebase.provider.FirebaseInitProvider"
278-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
279            android:authorities="com.hamode.appchat.firebaseinitprovider"
279-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
280            android:directBootAware="true"
280-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
281            android:exported="false"
281-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
282            android:initOrder="100" />
282-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6c469df59dc7c0788003b78a3b5a757f\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
283
284        <activity
284-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:23:9-27:75
285            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
285-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:24:13-93
286            android:excludeFromRecents="true"
286-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:25:13-46
287            android:exported="false"
287-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:26:13-37
288            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
288-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:27:13-72
289        <!--
290            Service handling Google Sign-In user revocation. For apps that do not integrate with
291            Google Sign-In, this service will never be started.
292        -->
293        <service
293-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:33:9-37:51
294            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
294-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:34:13-89
295            android:exported="true"
295-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:35:13-36
296            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
296-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:36:13-107
297            android:visibleToInstantApps="true" />
297-->[com.google.android.gms:play-services-auth:21.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3e01eb3a357ff39e73abedae28784719\transformed\jetified-play-services-auth-21.0.0\AndroidManifest.xml:37:13-48
298
299        <receiver
299-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:29:9-33:20
300            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
300-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:30:13-85
301            android:enabled="true"
301-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:31:13-35
302            android:exported="false" >
302-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:32:13-37
303        </receiver>
304
305        <service
305-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:35:9-38:40
306            android:name="com.google.android.gms.measurement.AppMeasurementService"
306-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:36:13-84
307            android:enabled="true"
307-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:37:13-35
308            android:exported="false" />
308-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:38:13-37
309        <service
309-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:39:9-43:72
310            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
310-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:40:13-87
311            android:enabled="true"
311-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:41:13-35
312            android:exported="false"
312-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:42:13-37
313            android:permission="android.permission.BIND_JOB_SERVICE" />
313-->[com.google.android.gms:play-services-measurement:22.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3474d7172a94f546a700433c9c747260\transformed\jetified-play-services-measurement-22.4.0\AndroidManifest.xml:43:13-69
314
315        <uses-library
315-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
316            android:name="androidx.window.extensions"
316-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
317            android:required="false" />
317-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
318        <uses-library
318-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
319            android:name="androidx.window.sidecar"
319-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
320            android:required="false" />
320-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\bfd09fc438435f94f0cfd5f3eb226602\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
321        <uses-library
321-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.9\transforms\9aeb0abcba29958216af2915c1c4fcb2\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:23:9-25:40
322            android:name="android.ext.adservices"
322-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.9\transforms\9aeb0abcba29958216af2915c1c4fcb2\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:24:13-50
323            android:required="false" />
323-->[androidx.privacysandbox.ads:ads-adservices:1.1.0-beta11] C:\Users\<USER>\.gradle\caches\8.9\transforms\9aeb0abcba29958216af2915c1c4fcb2\transformed\jetified-ads-adservices-1.1.0-beta11\AndroidManifest.xml:25:13-37
324
325        <activity
325-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\04f854c86687d4911c82dc9952d1fb33\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
326            android:name="com.google.android.gms.common.api.GoogleApiActivity"
326-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\04f854c86687d4911c82dc9952d1fb33\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
327            android:exported="false"
327-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\04f854c86687d4911c82dc9952d1fb33\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
328            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
328-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\04f854c86687d4911c82dc9952d1fb33\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
329
330        <provider
330-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
331            android:name="androidx.startup.InitializationProvider"
331-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
332            android:authorities="com.hamode.appchat.androidx-startup"
332-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
333            android:exported="false" >
333-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
334            <meta-data
334-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
335                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
335-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
336                android:value="androidx.startup" />
336-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\0e9a49b3d811c6474d4770eb72dfb836\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
337            <meta-data
337-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
338                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
338-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
339                android:value="androidx.startup" />
339-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
340        </provider>
341
342        <meta-data
342-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\82766f43c900f418c4a97a171253098c\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
343            android:name="com.google.android.gms.version"
343-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\82766f43c900f418c4a97a171253098c\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
344            android:value="@integer/google_play_services_version" />
344-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\82766f43c900f418c4a97a171253098c\transformed\jetified-play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
345
346        <service
346-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:28:9-34:19
347            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
347-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:29:13-103
348            android:exported="false" >
348-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:30:13-37
349            <meta-data
349-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:31:13-33:39
350                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
350-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:32:17-94
351                android:value="cct" />
351-->[com.google.android.datatransport:transport-backend-cct:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\09f5fdbe4736970d4dbce55ab96784fe\transformed\jetified-transport-backend-cct-3.1.9\AndroidManifest.xml:33:17-36
352        </service>
353        <service
353-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:26:9-30:19
354            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
354-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:27:13-117
355            android:exported="false"
355-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:28:13-37
356            android:permission="android.permission.BIND_JOB_SERVICE" >
356-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:29:13-69
357        </service>
358
359        <receiver
359-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:32:9-34:40
360            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
360-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:33:13-132
361            android:exported="false" />
361-->[com.google.android.datatransport:transport-runtime:3.1.9] C:\Users\<USER>\.gradle\caches\8.9\transforms\47593d1d1cd0a22c63545734134d2634\transformed\jetified-transport-runtime-3.1.9\AndroidManifest.xml:34:13-37
362        <receiver
362-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
363            android:name="androidx.profileinstaller.ProfileInstallReceiver"
363-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
364            android:directBootAware="false"
364-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
365            android:enabled="true"
365-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
366            android:exported="true"
366-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
367            android:permission="android.permission.DUMP" >
367-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
368            <intent-filter>
368-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
369                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
369-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
370            </intent-filter>
371            <intent-filter>
371-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
372                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
372-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
373            </intent-filter>
374            <intent-filter>
374-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
375                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
375-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
376            </intent-filter>
377            <intent-filter>
377-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
378                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
378-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\f197939ef85c16fb5482cdec31ca7f32\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
379            </intent-filter>
380        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
381        <activity
381-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
382            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
382-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
383            android:exported="false"
383-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
384            android:stateNotNeeded="true"
384-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
385            android:theme="@style/Theme.PlayCore.Transparent" />
385-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.9\transforms\3990cad49b40dbdc907c7d7f476580c0\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
386    </application>
387
388</manifest>
